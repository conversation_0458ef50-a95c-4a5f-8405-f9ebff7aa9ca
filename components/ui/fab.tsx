import React, { useMemo, useState, useEffect } from "react";
import { Pressable, StyleSheet, View as RNView, ViewStyle, StyleProp, Dimensions } from "react-native";
import Animated, {
  Easing,
  FadeIn,
  FadeOut,
  ZoomIn,
  ZoomOut,
  SlideInUp,
  SlideOutUp,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  SharedValue,
} from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { View } from "./view";
import { Text } from "./text";
import { Spinner } from "./spinner";

interface FABAction {
  icon: React.ReactNode;
  label?: string;
  onPress: () => void;
  disabled?: boolean;
  color?: string; // background for the action
  foregroundColor?: string; // icon color for the action
}

interface FABProps {
  icon: React.ReactNode;
  label?: string;
  onPress: () => void;

  actions?: FABAction[];
  position?: "bottom-right" | "bottom-left" | "bottom-center";
  offset?: { x?: number; y?: number };

  visible?: boolean;
  animationType?: "fade" | "scale" | "slide";
  animationDuration?: number;

  // Container/background color
  color?: string;
  // Content color (icon/text/spinner)
  foregroundColor?: string;

  size?: "small" | "medium" | "large";
  style?: StyleProp<ViewStyle>;
  iconStyle?: object;
  labelStyle?: object;

  // Shape and variant
  shape?: "circular" | "rounded";
  variant?: "standard" | "extended";
  /**
   * Convenience alias. If provided, overrides variant.
   * When true => variant = "extended", false => "standard"
   */
  extended?: boolean;

  disabled?: boolean;
  loading?: boolean;

  // Speed-dial open state control
  open?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Backdrop overlay color while actions are visible
  overlayColor?: string;
  // Opacity multiplier for the overlay (0..1)
  overlayOpacity?: number;
  // Percentage-based overlay opacity (0..100). Takes precedence over overlayOpacity.
  overlayOpacityPercent?: number;
  // Solid tint color to use for the overlay (e.g., '#000'). Used when overlayOpacityPercent is provided, or as a fallback.
  overlayTintColor?: string;
  // When variant="extended", where to place the label relative to the icon
  extendedLabelPosition?: "left" | "right";
  // For speed-dial actions, where to place the label relative to the mini-fab
  actionsLabelPosition?: "left" | "right" | "auto";
  // Optional text color for action labels
  actionLabelColor?: string;
  
  // New props
  elevation?: "elevated" | "flat";
  collapseOnScroll?: boolean;
  scrollY?: SharedValue<number>;
  scrollThreshold?: number;
  actionSize?: "small" | "medium" | "large" | number;
}

const FAB: React.FC<FABProps> = ({
  icon,
  label,
  onPress,

  actions = [],
  position = "bottom-right",
  offset = { x: 24, y: 24 },

  visible = true,
  animationType = "scale",
  animationDuration = 200,

  color = "#e4e4e4ff",
  foregroundColor = "#111",

  size = "medium",
  style,
  iconStyle,
  labelStyle,

  shape = "circular",
  variant: rawVariant = "standard",
  extended,

  disabled = false,
  loading = false,

  open: controlledOpen,
  onOpenChange,

  overlayColor = "rgba(0,0,0,0.2)",
  overlayOpacity = 1,
  overlayOpacityPercent,
  overlayTintColor,
  extendedLabelPosition = "right",
  actionsLabelPosition = "auto",
  actionLabelColor,
  
  // New props implementation
  elevation = "elevated",
  collapseOnScroll = false,
  scrollY,
  scrollThreshold = 50,
  actionSize,
}) => {
  // Derived variant from `extended` convenience prop
  const variant: "standard" | "extended" = extended !== undefined ? (extended ? "extended" : "standard") : rawVariant;

  // Controlled/uncontrolled "open"
  const [uncontrolledOpen, setUncontrolledOpen] = useState(false);
  const isControlled = typeof controlledOpen === "boolean";
  const open = isControlled ? (controlledOpen as boolean) : uncontrolledOpen;

  const insets = useSafeAreaInsets();

  // Visibility animation for the container (matches animationType loosely)
  const visibleProgress = useSharedValue(visible ? 1 : 0);

  // Progress for open/close of speed-dial actions and icon rotation
  const dialProgress = useSharedValue(open ? 1 : 0);

  // Extended FAB animation progress (width + label)
  const extendedProgress = useSharedValue(variant === "extended" ? 1 : 0);
  
  // Scroll-based collapse progress
  const scrollCollapseProgress = useSharedValue(1);
  const lastScrollY = useSharedValue(0);

  // Measure label width for smooth extended width interpolation (shared to be accessed in worklets)
  const labelWidth = useSharedValue(0);
  const onMeasureLabel = (w: number) => {
    labelWidth.value = w;
  };

  const mergedOffset = useMemo(
    () => ({
      x: 24,
      y: 24,
      ...offset,
    }),
    [offset]
  );

  const sizeMap = useMemo(
    () => ({
      small: 40,
      medium: 56,
      large: 72,
    }),
    []
  );

  const height = sizeMap[size];
  const baseFabSize = height;
  
  // Calculate action FAB size
  const actionFabSize = useMemo(() => {
    if (typeof actionSize === "number") return actionSize;
    if (actionSize === "small") return 40;
    if (actionSize === "large") return 56;
    // Default to one size smaller than main FAB
    if (size === "large") return 56;
    if (size === "medium") return 40;
    return 32; // extra small for small main FAB
  }, [actionSize, size]);

  // Max width for action labels so they extend without pushing the icon
  const maxLabelWidth = useMemo(() => {
    const screenW = Dimensions.get("window").width || 360;
    // Use up to 65% of screen width or 320px, whichever is smaller
    return Math.min(screenW * 0.65, 320);
  }, []);

  // Clamp helper
  const clamp = (n: number, min = 0, max = 1) => Math.max(min, Math.min(max, n));

  // Compute overlay alpha from percentage or legacy fraction
  const overlayAlpha =
    overlayOpacityPercent !== undefined
      ? clamp(overlayOpacityPercent / 100)
      : overlayOpacity !== undefined
      ? overlayOpacity > 1
        ? clamp(overlayOpacity / 100)
        : clamp(overlayOpacity)
      : 1;

  // Choose overlay base color. If percentage is provided, prefer a solid tint to allow full opacity.
  const baseOverlayColor =
    overlayOpacityPercent !== undefined
      ? (overlayTintColor ?? "#000")
      : (overlayTintColor ?? overlayColor ?? "#000");

  // Heuristic to decide if backdrop is dark (helps default label contrast)
  const isDarkBackdrop =
    typeof baseOverlayColor === "string" &&
    (
      baseOverlayColor.toLowerCase() === "#000" ||
      baseOverlayColor.toLowerCase() === "#000000" ||
      baseOverlayColor.startsWith("rgb(0,0,0") ||
      baseOverlayColor.startsWith("rgba(0,0,0")
    );

  // Decide default action label color (user can override via actionLabelColor or labelStyle)
  const actionLabelTextColor = actionLabelColor ?? (isDarkBackdrop && overlayAlpha >= 0.4 ? "#fff" : foregroundColor);

  // Determine which side action labels should render on
  const actionLabelSide = useMemo(() => {
    if (actionsLabelPosition === "auto") {
      // Auto-position based on FAB position
      return position === "bottom-left" ? "right" : "left";
    }
    return actionsLabelPosition;
  }, [actionsLabelPosition, position]);

  const toggleOpen = () => {
    if (disabled || loading) return;
    if (actions.length === 0) {
      onPress?.();
      return;
    }
    const next = !open;
    if (!isControlled) setUncontrolledOpen(next);
    onOpenChange?.(next);
  };
  
  // Handle scroll-based collapse
  useEffect(() => {
    if (!collapseOnScroll || !scrollY) return;

    // Use a derived value to track scroll changes
    const scrollListener = () => {
      'worklet';
      const value = scrollY.value;
      const delta = value - lastScrollY.value;
      lastScrollY.value = value;

      if (Math.abs(delta) < 2) return; // Ignore tiny movements

      if (delta > 0 && value > scrollThreshold) {
        // Scrolling down - collapse
        scrollCollapseProgress.value = withTiming(0, { duration: 200 });
      } else if (delta < 0) {
        // Scrolling up - expand
        scrollCollapseProgress.value = withTiming(1, { duration: 200 });
      }
    };

    // Note: In reanimated v3, we would typically use useDerivedValue for this
    // For now, this effect will run when scrollY changes
    scrollListener();
  }, [collapseOnScroll, scrollY, scrollThreshold, scrollCollapseProgress, lastScrollY]);

  React.useEffect(() => {
    visibleProgress.value =
      animationType === "fade"
        ? withTiming(visible ? 1 : 0, { duration: animationDuration, easing: Easing.out(Easing.quad) })
        : animationType === "slide"
        ? withSpring(visible ? 1 : 0, { damping: 14, stiffness: 140 })
        : // "scale"
          withSpring(visible ? 1 : 0, { damping: 16, stiffness: 160 });
  }, [visible, animationType, animationDuration, visibleProgress]);

  React.useEffect(() => {
    dialProgress.value = withTiming(open ? 1 : 0, { duration: 220, easing: Easing.out(Easing.cubic) });
  }, [open, dialProgress]);

  React.useEffect(() => {
    // Combine variant state with scroll collapse
    const targetProgress = variant === "extended" ? 1 : 0;
    extendedProgress.value = withTiming(targetProgress, { duration: 180, easing: Easing.out(Easing.cubic) });
  }, [variant, extendedProgress]);

  const extendedLabelAnim = useAnimatedStyle(() => {
    const dir = extendedLabelPosition === "left" ? 8 : -8;
    const combinedProgress = collapseOnScroll 
      ? extendedProgress.value * scrollCollapseProgress.value 
      : extendedProgress.value;
    
    return {
      opacity: combinedProgress,
      transform: [
        { translateX: interpolate(combinedProgress, [0, 1], [dir, 0]) },
        { scale: interpolate(combinedProgress, [0, 1], [0.98, 1]) },
      ],
    };
  });

  const containerAnimatedStyle = useAnimatedStyle(() => {
    const p = visibleProgress.value;

    if (animationType === "fade") {
      return {
        opacity: p,
        transform: [{ scale: 1 }, { translateY: 0 }],
      };
    }

    if (animationType === "slide") {
      const translateY = interpolate(p, [0, 1], [80, 0]);
      const opacity = interpolate(p, [0, 1], [0, 1]);
      return {
        opacity,
        transform: [{ scale: 1 }, { translateY }],
      };
    }

    // scale
    const scale = interpolate(p, [0, 1], [0.6, 1]);
    const opacity = interpolate(p, [0, 1], [0, 1]);
    return {
      opacity,
      transform: [{ scale }, { translateY: 0 }],
    };
  });

  // Icon rotation when opening dial (e.g., + rotates to x)
  const iconRotateStyle = useAnimatedStyle(() => {
    const rotate = `${interpolate(dialProgress.value, [0, 1], [0, 45])}deg`;
    return {
      transform: [{ rotate }],
    };
  });

  // Extended FAB width and inner label animation
  const extendedFabStyle = useAnimatedStyle(() => {
    const extraLabelSpace = labelWidth.value + 20; // 10px padding left/right around label
    const combinedProgress = collapseOnScroll 
      ? extendedProgress.value * scrollCollapseProgress.value 
      : extendedProgress.value;
    
    const width = baseFabSize + interpolate(combinedProgress, [0, 1], [0, extraLabelSpace]);
    const borderRadius =
      shape === "circular"
        ? baseFabSize / 2
        : // rounded corners for extended
          Math.min(20, baseFabSize / 2);

    return {
      width,
      height: baseFabSize,
      borderRadius,
      backgroundColor: color,
      opacity: disabled ? 0.6 : 1,
    };
  });

  const standardFabStyle = useAnimatedStyle(() => {
    const borderRadius =
      shape === "circular" ? baseFabSize / 2 : Math.min(18, baseFabSize / 2);
    return {
      width: baseFabSize,
      height: baseFabSize,
      borderRadius,
      backgroundColor: color,
      opacity: disabled ? 0.6 : 1,
    };
  });

  const getPositionStyle = (): ViewStyle => {
    const base: any = {
      position: "absolute",
      bottom: mergedOffset.y + insets.bottom,
    };
    if (position === "bottom-right") base.right = mergedOffset.x;
    if (position === "bottom-left") base.left = mergedOffset.x;
    if (position === "bottom-center") {
      base.left = "50%";
      // For extended variant, we translate by half of current width; we approximate with baseFabSize
      base.transform = [{ translateX: -baseFabSize / 2 }];
    }
    return base;
  };
  
  // Get elevation styles based on elevation prop
  const getElevationStyle = () => {
    if (elevation === "flat") {
      return styles.fabFlat;
    }
    return styles.fabShadow;
  };

  const renderActions = () => {
    if (!open || actions.length === 0) return null;

    // Choose per-type animations for speed-dial items
    const Enter = animationType === "scale" ? ZoomIn : animationType === "fade" ? FadeIn : SlideInUp;
    const Exit = animationType === "scale" ? ZoomOut : animationType === "fade" ? FadeOut : SlideOutUp;
    
    // Calculate action border radius based on shape
    const actionBorderRadius = shape === "circular" 
      ? actionFabSize / 2 
      : Math.min(16, actionFabSize / 2);

    return (
      <>
        {actions.map((action, idx) => {
          const distance = (idx + 1) * (actionFabSize + 16);
          return (
            <Animated.View
              key={idx}
              entering={Enter.duration(200).delay(idx * 40)}
              exiting={Exit.duration(160).delay((actions.length - 1 - idx) * 30)}
              style={[
                styles.actionContainer,
                {
                  bottom: distance,
                  // respect horizontal side based on main position
                  left: position === "bottom-left" ? 0 : undefined,
                  right: position !== "bottom-left" ? 0 : undefined,
                },
              ]}
            >
              <RNView style={[styles.actionRow]}>
                {/* Label on left side */}
                {action.label && actionLabelSide === "left" && (
                  <Animated.View
                    entering={FadeIn.duration(200).delay(idx * 40 + 100)}
                    exiting={FadeOut.duration(100)}
                    style={[styles.labelWrapper, { maxWidth: maxLabelWidth }]}
                  >
                    <Text
                      style={[styles.label, { color: actionLabelTextColor }, labelStyle]}

                    >
                      {action.label}
                    </Text>
                  </Animated.View>
                )}
                
                {/* Mini-FAB */}
                <Pressable
                  disabled={action.disabled}
                  onPress={() => {
                    if (action.disabled) return;
                    action.onPress?.();
                    // Auto close after action tap
                    if (!isControlled) setUncontrolledOpen(false);
                    onOpenChange?.(false);
                  }}
                  style={[
                    styles.fabBase,
                    getElevationStyle(),
                    {
                      width: actionFabSize,
                      height: actionFabSize,
                      borderRadius: actionBorderRadius,
                      backgroundColor: action.color ?? color,
                      opacity: action.disabled ? 0.5 : 1,
                    },
                  ]}
                >
                  <RNView style={[iconStyle]}>
                    {action.icon}
                  </RNView>
                </Pressable>

                {/* Label on right side */}
                {action.label && actionLabelSide === "right" && (
                  <Animated.View
                    entering={FadeIn.duration(200).delay(idx * 40 + 100)}
                    exiting={FadeOut.duration(100)}
                    style={[styles.labelWrapper, { maxWidth: maxLabelWidth }]}
                  >
                    <Text
                      style={[styles.label, { color: actionLabelTextColor }, labelStyle]}
                    >
                      {action.label}
                    </Text>
                  </Animated.View>
                )}
              </RNView>
            </Animated.View>
          );
        })}
      </>
    );
  };

  return (
    <View pointerEvents="box-none" style={StyleSheet.absoluteFill}>
      {open && actions.length > 0 && (
        <>
          {/* Wrap overlay in Animated.View and apply static opacity on child to avoid reanimated warning */}
          <Animated.View
            entering={FadeIn.duration(150)}
            exiting={FadeOut.duration(150)}
            style={[StyleSheet.absoluteFillObject]}
          >
            <RNView
              style={[
                StyleSheet.absoluteFillObject,
                { backgroundColor: baseOverlayColor, opacity: overlayAlpha },
              ]}
            />
          </Animated.View>
          <Pressable style={StyleSheet.absoluteFill} onPress={toggleOpen} />
        </>
      )}

      <View style={[getPositionStyle(), style]}>
        {/* Actions */}
        {renderActions()}

        {/* Main FAB */}
        <Animated.View style={[containerAnimatedStyle, {width:200, display:'flex', alignItems:'flex-end'}]}>
          <Pressable
            onPress={actions.length ? toggleOpen : onPress}
            disabled={disabled || loading}
            style={({ pressed }) => [
              getElevationStyle(),
              { opacity: pressed && !disabled && !loading ? 0.85 : 1 },
            ]}
          >
            <Animated.View
              style={[
                styles.fabBase,
                variant === "extended" ? extendedFabStyle : standardFabStyle,
                { paddingHorizontal: variant === "extended" ? 12 : 0, flexDirection: "row" },
              ]}
            >
              {/* Extended label on left */}
              {variant === "extended" && extendedLabelPosition === "left" && !!label && (
                <Animated.View
                  style={[extendedLabelAnim, styles.extendedLabelRow]}
                  onLayout={(e) => onMeasureLabel(e.nativeEvent.layout.width)}
                >
                  <Text
                    style={[
                      styles.extendedLabel,
                      { color: foregroundColor },
                      labelStyle,
                    ]}
                    numberOfLines={1}
                  >
                    {label}
                  </Text>
                </Animated.View>
              )}
  
              {/* Icon or Spinner */}
              <RNView
                style={[
                  styles.iconWrapper,
                  { width: baseFabSize, height: baseFabSize},
                ]}
              >
                {loading ? (
                  <Spinner color={foregroundColor} />
                ) : actions.length ? (
                  <Animated.View style={[iconRotateStyle, iconStyle]}>
                    {icon }
                  </Animated.View>
                ) : (
                  <RNView style={iconStyle}>{icon}</RNView>
                )}
              </RNView>
  
              {/* Extended label on right */}
              {variant === "extended" && extendedLabelPosition === "right" && !!label && (
                <Animated.View
                  style={[extendedLabelAnim, styles.extendedLabelRow]}
                  onLayout={(e) => onMeasureLabel(e.nativeEvent.layout.width)}
                >
                  <Text
                    style={[
                      styles.extendedLabel,
                      { color: foregroundColor },
                      labelStyle,
                    ]}
                    numberOfLines={1}
                  >
                    {label}
                  </Text>
                </Animated.View>
              )}
            </Animated.View>
          </Pressable>

          {/* For non-extended, keep optional caption below for parity with previous API */}
          {variant !== "extended" && !!label && (
            <Text style={[styles.captionBelow, labelStyle]}>{label}</Text>
          )}
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  fabShadow: {
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    zIndex: 2,
  },
  fabFlat: {
    elevation: 0,
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    zIndex: 2,
  },
  fabBase: {
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  iconWrapper: {
    justifyContent: "center",
    alignItems: "center",
  },
  extendedLabelRow: {
    paddingRight: 8,
    // paddingLeft: 4,
  },
  extendedLabel: {
    fontSize: 14,
    fontWeight: "600",
  },
  captionBelow: {
    marginTop: 8,
    textAlign: "center",
    fontSize: 14,
    fontWeight: "500",
  },
  actionContainer: {
    position: "absolute",
    alignItems: "center",
    zIndex: 1,
  },
  actionRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    minHeight: 56,
    gap: 16,
  },
  labelWrapper: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    // backgroundColor: "rgba(0, 0, 0, 0.85)",
  },
  label: {
    fontSize: 12,
    color: "#fff",
    // flexShrink: 1, 
    textAlign: "right", // Aligns text to the right within the label background
  },
});

export default FAB;

