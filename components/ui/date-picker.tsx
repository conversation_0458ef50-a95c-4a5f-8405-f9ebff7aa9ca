import { BottomSheet, useBottomSheet } from '@/components/ui/bottom-sheet';
import { Button } from '@/components/ui/button';
import { Icon } from '@/components/ui/icon';

import { Text } from '@/components/ui/text';
import { View } from '@/components/ui/view';
import { useThemeColor } from '@/hooks/useThemeColor';
import { BORDER_RADIUS, CORNERS, FONT_SIZE, HEIGHT } from '@/theme/globals';
import {
  Calendar,
  CalendarClock,
  ChevronLeft,
  ChevronRight,
  Clock,
  CalendarRange,
  ArrowRight,
} from 'lucide-react-native';
import { useCallback, useMemo, useState, useEffect } from 'react';
import { TextStyle, TouchableOpacity, ViewStyle, Pressable } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate,
  runOnJS,
  useAnimatedRef,
  withTiming,
  FadeIn,
  FadeOut,
  SharedValue,
} from 'react-native-reanimated';

const ITEM_HEIGHT = 50;
const VISIBLE_ITEMS = 5;
const PICKER_HEIGHT = ITEM_HEIGHT * VISIBLE_ITEMS;

// #region Wheel Picker Components
interface WheelPickerItem {
  label: string;
  value: any;
}

interface WheelPickerProps {
  data: WheelPickerItem[];
  onValueChange: (value: any) => void;
  selectedValue: any;
  textColor: string;
  primaryColor: string;
}

interface WheelPickerRenderItemProps {
  item: WheelPickerItem;
  index: number;
  scrollY: SharedValue<number>;
  selectedValue: any;
  textColor: string;
  primaryColor: string;
}

const WheelPickerRenderItem: React.FC<WheelPickerRenderItemProps> = ({
  item,
  index,
  scrollY,
  selectedValue,
  textColor,
  primaryColor,
}) => {
  const animatedStyle = useAnimatedStyle(() => {
    const position = index * ITEM_HEIGHT - scrollY.value;
    const scale = interpolate(
      position,
      [-ITEM_HEIGHT * 2, -ITEM_HEIGHT, 0, ITEM_HEIGHT, ITEM_HEIGHT * 2],
      [0.7, 0.9, 1, 0.9, 0.7],
      'clamp'
    );
    const opacity = interpolate(
      position,
      [-ITEM_HEIGHT * 2, -ITEM_HEIGHT, 0, ITEM_HEIGHT, ITEM_HEIGHT * 2],
      [0.3, 0.6, 1, 0.6, 0.3],
      'clamp'
    );
    return {
      transform: [{ scale }],
      opacity,
    };
  });

  const isSelected = item.value === selectedValue;

  return (
    <Animated.View
      style={[
        {
          height: ITEM_HEIGHT,
          justifyContent: 'center',
          alignItems: 'center',
        },
        animatedStyle,
      ]}
    >
      <Text
        style={{
          fontSize: 20,
          color: isSelected ? primaryColor : textColor,
          fontWeight: isSelected ? '600' : '400',
        }}
      >
        {item.label}
      </Text>
    </Animated.View>
  );
};

const WheelPicker: React.FC<WheelPickerProps> = ({
  data,
  onValueChange,
  selectedValue,
  textColor,
  primaryColor,
}) => {
  const scrollY = useSharedValue(0);
  const scrollViewRef = useAnimatedRef<Animated.ScrollView>();

  const selectedIndex = useMemo(
    () => data.findIndex((item) => item.value === selectedValue),
    [data, selectedValue]
  );

  useEffect(() => {
    if (selectedIndex !== -1 && scrollViewRef.current) {
      const y = selectedIndex * ITEM_HEIGHT;
      setTimeout(() => {
        scrollViewRef.current?.scrollTo({ y, animated: false });
      }, 50);
    }
  }, [selectedIndex, scrollViewRef]);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
    onMomentumEnd: (event) => {
      const y = event.contentOffset.y;
      const index = Math.round(y / ITEM_HEIGHT);
      if (data[index] && data[index].value !== selectedValue) {
        runOnJS(onValueChange)(data[index].value);
      }
    },
  });

  const pickerPadding = (PICKER_HEIGHT - ITEM_HEIGHT) / 2;

  return (
    <View style={{ height: PICKER_HEIGHT }}>
      <Animated.ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        snapToInterval={ITEM_HEIGHT}
        decelerationRate="fast"
        contentContainerStyle={{ paddingVertical: pickerPadding }}
      >
        {data.map((item, index) => (
          <WheelPickerRenderItem
            key={item.value}
            item={item}
            index={index}
            scrollY={scrollY}
            selectedValue={selectedValue}
            textColor={textColor}
            primaryColor={primaryColor}
          />
        ))}
      </Animated.ScrollView>
      <View
        style={{
          position: 'absolute',
          top: pickerPadding,
          left: 20,
          right: 20,
          height: ITEM_HEIGHT,
          borderRadius: CORNERS,
          backgroundColor: primaryColor,
          opacity: 0.1,
        }}
        pointerEvents="none"
      />
    </View>
  );
};
// #endregion

// #region Calendar Components
interface DayCellProps {
  day: number;
  onSelect: (day: number) => void;
  isSelected: boolean;
  isToday: boolean;
  disabled: boolean;
  inRange: boolean;
  isRangeStart: boolean;
  isRangeEnd: boolean;
  primaryColor: string;
  primaryForegroundColor: string;
  textColor: string;
  mutedForegroundColor: string;
}

const DayCell: React.FC<DayCellProps> = ({
  day,
  onSelect,
  isSelected,
  isToday,
  disabled,
  inRange,
  isRangeStart,
  isRangeEnd,
  primaryColor,
  primaryForegroundColor,
  textColor,
  mutedForegroundColor,
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const containerAnimatedStyle = useAnimatedStyle(() => ({
    backgroundColor: withTiming(
      inRange && !isRangeStart && !isRangeEnd ? primaryColor : 'transparent',
      { duration: 200 }
    ),
  }));

  const cellStyle: ViewStyle = {
    width: 40,
    height: 40,
    borderRadius: CORNERS,
    backgroundColor:
      isSelected || isRangeStart || isRangeEnd ? primaryColor : 'transparent',
    borderWidth: isToday && !isSelected && !inRange ? 1 : 0,
    borderColor: primaryColor,
    justifyContent: 'center',
    alignItems: 'center',
  };

  const textStyle: TextStyle = {
    color:
      isSelected || isRangeStart || isRangeEnd
        ? primaryForegroundColor
        : inRange
        ? primaryForegroundColor
        : disabled
        ? mutedForegroundColor
        : textColor,
    fontWeight:
      isSelected || isRangeStart || isRangeEnd || isToday ? '600' : '400',
    fontSize: FONT_SIZE,
  };

  return (
    <Animated.View
      style={[
        { flex: 1, alignItems: 'center' },
        containerAnimatedStyle,
        isRangeStart && {
          borderTopLeftRadius: CORNERS,
          borderBottomLeftRadius: CORNERS,
        },
        isRangeEnd && {
          borderTopRightRadius: CORNERS,
          borderBottomRightRadius: CORNERS,
        },
      ]}
    >
      <Pressable
        onPress={() => !disabled && onSelect(day)}
        onPressIn={() => (scale.value = withTiming(0.9, { duration: 100 }))}
        onPressOut={() => (scale.value = withTiming(1, { duration: 100 }))}
        disabled={disabled}
        style={{ opacity: disabled ? 0.3 : 1 }}
      >
        <Animated.View style={[cellStyle, animatedStyle]}>
          <Text style={textStyle}>{day}</Text>
        </Animated.View>
      </Pressable>
    </Animated.View>
  );
};
// #endregion

export interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

// Conditional typing based on mode
interface BaseDatePickerProps {
  label?: string;
  labelPosition?: 'inside' | 'above';
  defaultToCurrentDate?: boolean;
  error?: string;
  placeholder?: string;
  disabled?: boolean;
  style?: ViewStyle;
  minimumDate?: Date;
  maximumDate?: Date;
  timeFormat?: '12' | '24';
  variant?: 'filled' | 'outline' | 'group';
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
}

interface DatePickerPropsRange extends BaseDatePickerProps {
  mode: 'range';
  value?: DateRange;
  onChange?: (value: DateRange | undefined) => void;
}

interface DatePickerPropsDate extends BaseDatePickerProps {
  mode?: 'date' | 'time' | 'datetime';
  value?: Date;
  onChange?: (value: Date | undefined) => void;
}

export type DatePickerProps = DatePickerPropsRange | DatePickerPropsDate;

const MONTHS = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

// Generate year range (current year ± 50 years)
const currentYear = new Date().getFullYear();
const YEARS = Array.from({ length: 101 }, (_, i) => currentYear - 50 + i);

// Type guard to check if value is DateRange
const isDateRange = (
  value: Date | DateRange | undefined
): value is DateRange => {
  return (
    value !== undefined &&
    typeof value === 'object' &&
    value !== null &&
    'startDate' in value &&
    'endDate' in value
  );
};

export function DatePicker(props: DatePickerProps) {
  const {
    label,
    labelPosition = 'inside',
    defaultToCurrentDate = false,
    error,
    placeholder = 'Select date',
    disabled = false,
    style,
    minimumDate,
    maximumDate,
    timeFormat = '24',
    variant = 'filled',
    labelStyle,
    errorStyle,
  } = props;

  const mode = props.mode || 'date';
  const value = props.value;
  const onChange = props.onChange;

  const { isVisible, open, close } = useBottomSheet();
  const [isFocused, setIsFocused] = useState(false);
  const [calendarAnimationDirection, setCalendarAnimationDirection] = useState<
    'next' | 'prev'
  >('next');

  // Get the current date for navigation, prioritizing single date or range start date
  const getCurrentDate = useCallback(() => {
    if (mode === 'range') {
      const rangeValue = isDateRange(value)
        ? value
        : { startDate: null, endDate: null };
      return rangeValue.startDate || new Date();
    }
    return (value as Date) || new Date();
  }, [value, mode]);

  const [currentDate, setCurrentDate] = useState(() => getCurrentDate());
  const [viewMode, setViewMode] = useState<'date' | 'time' | 'month' | 'year'>(
    'date'
  );
  const [showMonthPicker, setShowMonthPicker] = useState(false);
  const [showYearPicker, setShowYearPicker] = useState(false);

  // Range selection state for temporary storage during selection
  const [tempRange, setTempRange] = useState<DateRange>(() =>
    mode === 'range' && isDateRange(value)
      ? value
      : { startDate: null, endDate: null }
  );

  // Theme colors
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');
  const primaryColor = useThemeColor({}, 'primary');
  const primaryForegroundColor = useThemeColor({}, 'primaryForeground');
  const mutedColor = useThemeColor({}, 'muted');
  const textMutedColor = useThemeColor({}, 'textMuted');
  const mutedForegroundColor = useThemeColor({}, 'mutedForeground');
  const textColor = useThemeColor({}, 'text');
  const errorColor = useThemeColor({}, 'red');

  // Set default current date if enabled and no value is provided - moved to useEffect
  useEffect(() => {
    if (defaultToCurrentDate && !value) {
      const today = new Date();
      if (mode === 'range') {
        const defaultRange: DateRange = { startDate: today, endDate: null };
        (onChange as (value: DateRange | undefined) => void)?.(defaultRange);
      } else {
        (onChange as (value: Date | undefined) => void)?.(today);
      }
    }
  }, [defaultToCurrentDate, value, onChange, mode]);

  const formatDisplayValue = useCallback(() => {
    if (mode === 'range') {
      const rangeValue = isDateRange(value)
        ? value
        : { startDate: null, endDate: null };

      if (!rangeValue.startDate && !rangeValue.endDate) {
        return placeholder;
      }

      const startStr = rangeValue.startDate
        ? rangeValue.startDate.toLocaleDateString()
        : '';
      const endStr = rangeValue.endDate
        ? rangeValue.endDate.toLocaleDateString()
        : '';

      if (startStr && endStr) {
        return `${startStr} - ${endStr}`;
      } else if (startStr) {
        return `${startStr} - Select end date`;
      } else if (endStr) {
        return `Select start date - ${endStr}`;
      }
      return placeholder;
    }

    const dateValue = value as Date;
    if (!dateValue) return placeholder;

    switch (mode) {
      case 'time':
        if (timeFormat === '12') {
          return dateValue.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          });
        }
        return dateValue.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        });
      case 'datetime':
        const timeStr =
          timeFormat === '12'
            ? dateValue.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
              })
            : dateValue.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
              });
        return `${dateValue.toLocaleDateString()} ${timeStr}`;
      default:
        return dateValue.toLocaleDateString();
    }
  }, [value, mode, placeholder, timeFormat]);

  // Helper function to check if a date is disabled
  const isDateDisabled = useCallback(
    (date: Date) => {
      if (minimumDate && date < minimumDate) return true;
      if (maximumDate && date > maximumDate) return true;
      return false;
    },
    [minimumDate, maximumDate]
  );

  // Helper function to check if a date is in range
  const isDateInRange = useCallback(
    (date: Date) => {
      if (mode !== 'range' || !tempRange.startDate || !tempRange.endDate) {
        return false;
      }

      // Create new date objects to avoid mutation
      const startDate = new Date(tempRange.startDate);
      const endDate = new Date(tempRange.endDate);
      const checkDate = new Date(date);

      // Normalize dates for comparison (remove time)
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(0, 0, 0, 0);
      checkDate.setHours(0, 0, 0, 0);

      return checkDate >= startDate && checkDate <= endDate;
    },
    [mode, tempRange]
  );

  // Helper function to check if a date is a range endpoint
  const isRangeEndpoint = useCallback(
    (date: Date) => {
      if (mode !== 'range') {
        return { isStart: false, isEnd: false };
      }

      const normalizedDate = new Date(date);
      normalizedDate.setHours(0, 0, 0, 0);

      const isStart =
        tempRange.startDate &&
        new Date(tempRange.startDate).setHours(0, 0, 0, 0) ===
          normalizedDate.getTime();
      const isEnd =
        tempRange.endDate &&
        new Date(tempRange.endDate).setHours(0, 0, 0, 0) ===
          normalizedDate.getTime();

      return { isStart: !!isStart, isEnd: !!isEnd };
    },
    [mode, tempRange]
  );

  // Memoized calendar calculations
  const calendarData = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Create calendar grid with proper positioning
    const weeks: (number | null)[][] = [];
    let currentWeek: (number | null)[] = [];

    // Fill empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
      currentWeek.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      currentWeek.push(day);

      // If week is complete (7 days) or it's the last day, start a new week
      if (currentWeek.length === 7) {
        weeks.push([...currentWeek]);
        currentWeek = [];
      }
    }

    // Add the last incomplete week if it exists
    if (currentWeek.length > 0) {
      // Fill remaining cells with null
      while (currentWeek.length < 7) {
        currentWeek.push(null);
      }
      weeks.push(currentWeek);
    }

    // Ensure 6 weeks are always rendered for constant height
    while (weeks.length < 6) {
      weeks.push(Array(7).fill(null));
    }

    return { weeks, year, month, daysInMonth };
  }, [currentDate]);

  const handleRangeSelect = (day: number) => {
    const selectedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    // Check if date is disabled
    if (isDateDisabled(selectedDate)) return;

    // If no start date or both dates are selected, start fresh
    if (!tempRange.startDate || (tempRange.startDate && tempRange.endDate)) {
      setTempRange({
        startDate: selectedDate,
        endDate: null,
      });
    } else {
      // We have a start date but no end date
      const startDate = tempRange.startDate;

      if (selectedDate < startDate) {
        // If selected date is before start date, make it the new start date
        setTempRange({
          startDate: selectedDate,
          endDate: null,
        });
      } else {
        // Selected date is after start date, make it the end date
        setTempRange({
          startDate: startDate,
          endDate: selectedDate,
        });
      }
    }
  };

  const handleDateSelect = (day: number) => {
    if (mode === 'range') {
      handleRangeSelect(day);
      return;
    }

    const newDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    // Check if date is disabled
    if (isDateDisabled(newDate)) return;

    setCurrentDate(newDate);

    if (mode === 'date') {
      (onChange as (value: Date | undefined) => void)?.(newDate);
      handleClosePicker();
    } else if (mode === 'datetime') {
      setViewMode('time');
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCalendarAnimationDirection(direction);
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const handleConfirm = () => {
    if (mode === 'range') {
      (onChange as (value: DateRange | undefined) => void)?.(tempRange);
    }
    else {
      (onChange as (value: Date | undefined) => void)?.(currentDate);
    }
    handleClosePicker();
  };

  const resetToToday = () => {
    const today = new Date();
    setCurrentDate(today);

    if (mode === 'range') {
      setTempRange({ startDate: today, endDate: null });
    } else if (mode === 'date') {
      (onChange as (value: Date | undefined) => void)?.(today);
      handleClosePicker();
    }
  };

  const clearSelection = () => {
    if (mode === 'range') {
      setTempRange({ startDate: null, endDate: null });
      (onChange as (value: DateRange | undefined) => void)?.(undefined);
    } else {
      (onChange as (value: Date | undefined) => void)?.(undefined);
    }
  };

  const renderMonthYearHeader = () => (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 24,
        paddingHorizontal: 8,
      }}
    >
      <TouchableOpacity
        onPress={() => navigateMonth('prev')}
        style={{
          padding: 5,
          borderRadius: CORNERS,
        }}
      >
        <ChevronLeft size={20} color={textColor} />
      </TouchableOpacity>

      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 12,
          marginHorizontal: 12,
        }}
      >
        <TouchableOpacity
          onPress={() => setShowMonthPicker(true)}
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 10,
            borderRadius: CORNERS,
          }}
        >
          <Text variant="subtitle" style={{ marginRight: 4, fontSize: 16 }}>
            {MONTHS[calendarData.month]}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setShowYearPicker(true)}
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 10,
            borderRadius: CORNERS,
          }}
        >
          <Text variant="subtitle" style={{ marginRight: 4, fontSize: 16 }}>
            {calendarData.year}
          </Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        onPress={() => navigateMonth('next')}
        style={{
          padding: 5,
          borderRadius: CORNERS,
        }}
      >
        <ChevronRight size={20} color={textColor} />
      </TouchableOpacity>
    </View>
  );

  const renderCalendar = () => (
    <View>
      {renderMonthYearHeader()}
      {/* Day headers */}
      <View
        style={{
          flexDirection: 'row',
          marginBottom: 12,
          paddingHorizontal: 4,
        }}
      >
        {DAYS.map((day) => (
          <View
            key={day}
            style={{
              flex: 1,
              alignItems: 'center',
            }}
          >
            <Text variant="caption" style={{ fontSize: 12, fontWeight: '600' }}>
              {day}
            </Text>
          </View>
        ))}
      </View>

      <Animated.View
        key={currentDate.getMonth()} // Force re-render on month change for animation
        entering={calendarAnimationDirection === 'next' ? FadeIn : FadeIn}
        exiting={
          calendarAnimationDirection === 'next' ? FadeOut : FadeOut
        }
      >
        {/* Calendar grid */}
        <View style={{ paddingHorizontal: 4 }}>
          {calendarData.weeks.map((week, weekIndex) => (
            <View
              key={weekIndex}
              style={{
                flexDirection: 'row',
                marginBottom: 4,
              }}
            >
              {week.map((day, dayIndex) => {
                if (!day) {
                  return <View key={dayIndex} style={{ flex: 1, height: 40 }} />;
                }

                const dayDate = new Date(
                  calendarData.year,
                  calendarData.month,
                  day
                );

                const isSelected =
                  value &&
                  !isDateRange(value) &&
                  value.getDate() === day &&
                  value.getMonth() === calendarData.month &&
                  value.getFullYear() === calendarData.year;

                const isToday =
                  new Date().getDate() === day &&
                  new Date().getMonth() === calendarData.month &&
                  new Date().getFullYear() === calendarData.year;

                const disabled = isDateDisabled(dayDate);
                const inRange = isDateInRange(dayDate);
                const { isStart, isEnd } = isRangeEndpoint(dayDate);

                return (
                  <DayCell
                    key={`${calendarData.month}-${day}`}
                    day={day}
                    onSelect={handleDateSelect}
                    isSelected={!!isSelected}
                    isToday={isToday}
                    disabled={disabled}
                    inRange={inRange}
                    isRangeStart={isStart}
                    isRangeEnd={isEnd}
                    primaryColor={primaryColor}
                    primaryForegroundColor={primaryForegroundColor}
                    textColor={textColor}
                    mutedForegroundColor={mutedForegroundColor}
                  />
                );
              })}
            </View>
          ))}
        </View>
      </Animated.View>

      {/* Range selection info */}
      {mode === 'range' && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: 16,
            padding: 20,
            paddingHorizontal: 36,
            backgroundColor: mutedColor,
            borderRadius: BORDER_RADIUS,
          }}
        >
          <Text variant="subtitle" style={{ flex: 1 }}>
            {tempRange.startDate
              ? `${tempRange.startDate.toLocaleDateString()}`
              : 'Start date'}
          </Text>

          <View
            style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}
          >
            <ArrowRight color={textColor} strokeWidth={3} />
          </View>

          <Text variant="subtitle" style={{ flex: 1, textAlign: 'right' }}>
            {tempRange.endDate
              ? `${tempRange.endDate.toLocaleDateString()}`
              : 'End date'}
          </Text>
        </View>
      )}
    </View>
  );

  const renderTimePicker = () => {
    const selectedHours = currentDate.getHours();
    const selectedMinutes = currentDate.getMinutes();

    const handleHourChange = (hour: number) => {
      const newDate = new Date(currentDate);
      if (timeFormat === '12') {
        const isPM = selectedHours >= 12;
        if (isPM && hour < 12) {
          newDate.setHours(hour + 12);
        } else if (!isPM && hour === 12) {
          // 12 AM
          newDate.setHours(0);
        } else {
          newDate.setHours(hour);
        }
      } else {
        newDate.setHours(hour);
      }
      setCurrentDate(newDate);
    };

    const handleMinuteChange = (minute: number) => {
      const newDate = new Date(currentDate);
      newDate.setMinutes(minute);
      setCurrentDate(newDate);
    };

    const handlePeriodChange = (period: 'am' | 'pm') => {
      const newDate = new Date(currentDate);
      const hours = newDate.getHours();
      if (period === 'pm' && hours < 12) {
        newDate.setHours(hours + 12);
      } else if (period === 'am' && hours >= 12) {
        newDate.setHours(hours - 12);
      }
      setCurrentDate(newDate);
    };

    const hoursData =
      timeFormat === '12'
        ? Array.from({ length: 12 }, (_, i) => ({
            label: String(i + 1),
            value: i + 1,
          })) // 1-12
        : Array.from({ length: 24 }, (_, i) => ({ label: String(i), value: i })); // 0-23

    const minutesData = Array.from({ length: 60 }, (_, i) => ({
      label: i.toString().padStart(2, '0'),
      value: i,
    }));

    const selectedHourValue =
      timeFormat === '12'
        ? selectedHours % 12 === 0
          ? 12
          : selectedHours % 12
        : selectedHours;

    return (
      <View
        style={{
          height: PICKER_HEIGHT,
          flexDirection: 'row',
          alignItems: 'center',
          gap: 12,
        }}
      >
        <View style={{ flex: 1 }}>
          <WheelPicker
            data={hoursData}
            selectedValue={selectedHourValue}
            onValueChange={handleHourChange}
            textColor={textColor}
            primaryColor={primaryColor}
          />
        </View>
        <View style={{ flex: 1 }}>
          <WheelPicker
            data={minutesData}
            selectedValue={selectedMinutes}
            onValueChange={handleMinuteChange}
            textColor={textColor}
            primaryColor={primaryColor}
          />
        </View>
        {timeFormat === '12' && (
          <View style={{ flex: 1 }}>
            <WheelPicker
              data={[
                { label: 'AM', value: 'am' },
                { label: 'PM', value: 'pm' },
              ]}
              selectedValue={selectedHours >= 12 ? 'pm' : 'am'}
              onValueChange={handlePeriodChange}
              textColor={textColor}
              primaryColor={primaryColor}
            />
          </View>
        )}
      </View>
    );
  };

  const renderMonthPicker = () => {
    const months = MONTHS.map((m, i) => ({ label: m, value: i }));
    return (
      <WheelPicker
        data={months}
        selectedValue={currentDate.getMonth()}
        onValueChange={(month) => {
          const newDate = new Date(currentDate);
          newDate.setMonth(month);
          setCurrentDate(newDate);
        }}
        textColor={textColor}
        primaryColor={primaryColor}
      />
    );
  };

  const renderYearPicker = () => {
    const years = YEARS.map((y) => ({ label: String(y), value: y }));
    return (
      <WheelPicker
        data={years}
        selectedValue={currentDate.getFullYear()}
        onValueChange={(year) => {
          const newDate = new Date(currentDate);
          newDate.setFullYear(year);
          setCurrentDate(newDate);
        }}
        textColor={textColor}
        primaryColor={primaryColor}
      />
    );
  };

  const getBottomSheetContent = () => {
    if (showMonthPicker) return renderMonthPicker();
    if (showYearPicker) return renderYearPicker();

    if (mode === 'datetime') {
      return viewMode === 'date' ? renderCalendar() : renderTimePicker();
    }

    if (mode === 'time') return renderTimePicker();
    return renderCalendar();
  };

  const getBottomSheetTitle = () => {
    if (showMonthPicker) return 'Select Month';
    if (showYearPicker) return 'Select Year';

    if (mode === 'datetime') {
      return viewMode === 'date' ? 'Select Date' : 'Select Time';
    }

    if (mode === 'time') return 'Select Time';

    if (mode === 'range') return 'Select Range';

    return 'Select Date';
  };

  const handleOpenPicker = () => {
    setCurrentDate(getCurrentDate());
    setViewMode('date');
    setShowMonthPicker(false);
    setShowYearPicker(false);
    setIsFocused(true);
    open();
  };

  const handleClosePicker = () => {
    setIsFocused(false);
    close();
    setShowMonthPicker(false);
    setShowYearPicker(false);
  };

  const triggerStyle: ViewStyle = {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: variant === 'group' ? 0 : 16,
    borderWidth: variant === 'group' ? 0 : 2,
    borderColor: error ? errorColor : isFocused ? primaryColor : borderColor,
    borderRadius: CORNERS,
    backgroundColor: variant === 'filled' ? cardColor : 'transparent',
    minHeight: variant === 'group' ? 'auto' : HEIGHT,
  };

  return (
    <>
            
    
      {/* Label positioned above when labelPosition is 'above' */}
      {label && labelPosition === 'above' && (
        <Text
          variant="caption"
          style={[
            {
              color: error ? errorColor : textMutedColor,
              marginBottom: 0,
            },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
      

      <TouchableOpacity
        style={[triggerStyle, disabled && { opacity: 0.5 }, style]}
        onPress={handleOpenPicker}
        disabled={disabled}
      >
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
          }}
        >
          <View
            style={{
              width: label && labelPosition === 'inside' ? 120 : 'auto',
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
            }}
          >
            {mode === 'time' ? (
              <Icon name={Clock} size={20} strokeWidth={1} />
            ) : mode === 'datetime' ? (
              <Icon name={CalendarClock} size={20} strokeWidth={1} />
            ) : mode === 'range' ? (
              <Icon name={CalendarRange} size={20} strokeWidth={1} />
            ) : (
              <Icon name={Calendar} size={20} strokeWidth={1} />
            )}

            {/* Label takes 1/3 of available width when present and position is 'inside' */}
            {label && labelPosition === 'inside' && (
              <View style={{ flex: 1 }}>
                <Text
                  variant="caption"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={[
                    {
                      color: error ? errorColor : textMutedColor,
                    },
                    labelStyle,
                  ]}
                >
                  {label}
                </Text>

                
              </View>
            )}
          </View>

          {/* Text takes 2/3 of available width when label is inside, or full width when no label or label is above */}
          <View style={{ flex: 1 }}>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={{
                color: value ? textColor : textMutedColor,
                fontSize: FONT_SIZE,
              }}
            >
              {formatDisplayValue()}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

        {/* Error Message */}
              {error && (
                <Text
                  style={[
                    {
                      // marginLeft: 14,
                      marginTop: 2,
                      fontSize: 12,
                      color: errorColor,
                    },
                    errorStyle,
                  ]}
                >
                  {error}
                </Text>
              )}

      <BottomSheet
        isVisible={isVisible}
        onClose={handleClosePicker}
        title={getBottomSheetTitle()}
        snapPoints={[0.7]}
        disablePanGesture={
          showMonthPicker ||
          showYearPicker ||
          mode === 'time' ||
          (mode === 'datetime' && viewMode === 'time')
        }
      >
        <View style={{ flex: 1 }}>
          {getBottomSheetContent()}

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingTop: 20,
              gap: 12,
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                gap: 8,
              }}
            >
              <Button size="sm" variant="outline" onPress={resetToToday}>
                Today
              </Button>

              <Button
                size="sm"
                variant="outline"
                onPress={() => {
                  handleClosePicker();
                  clearSelection();
                }}
              >
                {mode === 'range' ? 'Clear' : 'Cancel'}
              </Button>
            </View>

            {mode === 'datetime' && viewMode === 'date' ? (
              <Button
                size="sm"
                onPress={() => {
                  setShowMonthPicker(false);
                  setShowYearPicker(false);
                  setViewMode('time');
                }}
                style={{ flex: 1 }}
              >
                Next
              </Button>
            ) : (
              <Button size="sm" onPress={handleConfirm} style={{ flex: 1 }}>
                Done
              </Button>
            )}
          </View>
        </View>
      </BottomSheet>
    </>
  );
}