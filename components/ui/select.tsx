import React, { useEffect, useState } from "react";
import {
  Pressable,
  View,
  ViewStyle,
  TextStyle,
  Modal,
  FlatList,
  TouchableOpacity,
} from "react-native";
import { ChevronDown, LucideProps } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { Text } from "@/components/ui/text";
import { useThemeColor } from "@/hooks/useThemeColor";
import { BORDER_RADIUS, CORNERS, HEIGHT, FONT_SIZE } from "@/theme/globals";
import { useModeToggle } from "@/hooks/useModeToggle";
import { Separator } from "./separator";

export interface SelectOption {
  label: string;
  value: string | number;
}

export interface SelectProps {
  label?: string;
  error?: string;
  icon?: React.ComponentType<LucideProps>;
  placeholder?: string;
  value?: string | number;
  defaultValue?: string | number; // ✅ new
  options: SelectOption[];
  onChange: (value: string | number) => void;
  containerStyle?: ViewStyle;
  selectContainerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  variant?: "filled" | "outline";
  disabled?: boolean;
  optionTextStyle?: TextStyle;
  modalOverlayStyle?: ViewStyle;
  modalContentStyle?: ViewStyle;
}

export const Select = ({
  label,
  error,
  icon,
  placeholder = "Select...",
  value: controlledValue,
  defaultValue,
  options,
  onChange,
  containerStyle,
  selectContainerStyle,
  labelStyle,
  errorStyle,
  variant = "filled",
  disabled = false,
  optionTextStyle,
  modalOverlayStyle,
  modalContentStyle,
}: SelectProps) => {
  const { toggleMode, isDark } = useModeToggle();
  const [open, setOpen] = useState(false);
  const [internalValue, setInternalValue] = useState<
    string | number | undefined
  >(defaultValue);

  // Sync with controlled value if passed
  const isControlled = controlledValue !== undefined;
  const value = isControlled ? controlledValue : internalValue;

  useEffect(() => {
    if (defaultValue !== undefined && !isControlled) {
      setInternalValue(defaultValue);
    }
  }, [defaultValue, isControlled]);

  // Theme colors
  const cardColor = useThemeColor({}, "card");
  const textColor = useThemeColor({}, "text");
  const muted = useThemeColor({}, "textMuted");
  const borderColor = useThemeColor({}, "border");
  const primary = useThemeColor({}, "primary");
  const danger = useThemeColor({}, "red");
  const background = useThemeColor({}, "background");
  const secondaryForeground = useThemeColor({}, "secondaryForeground");

  const selectedOption = options.find((opt) => opt.value === value);

  const getVariantStyle = (): ViewStyle => {
    const base: ViewStyle = {
      borderRadius: CORNERS,
      flexDirection: "row",
      alignItems: "center",
      minHeight: HEIGHT,
      paddingHorizontal: 16,
      borderWidth: 1,
      borderColor: error ? danger : borderColor,
    };

    switch (variant) {
      case "outline":
        return { ...base, backgroundColor: "transparent" };
      case "filled":
      default:
        return {
          ...base,
          backgroundColor: disabled ? muted + "20" : cardColor,
        };
    }
  };

  const handleSelect = (val: string | number) => {
    if (!isControlled) {
      setInternalValue(val);
    }
    onChange(val);
    setOpen(false);
  };

  return (
    <View style={containerStyle}>
      {/* Label above */}
      {label && (
        <Text
          variant="caption"
          style={[
            { marginBottom: 8, marginLeft: 4, color: error ? danger : muted },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}

      {/* Select box */}
      <Pressable
        onPress={() => !disabled && setOpen(true)}
        style={[
          getVariantStyle(),
          selectContainerStyle,
          disabled && { opacity: 0.6 },
        ]}
      >
        {/* Left icon */}
        {icon && (
          <Icon
            name={icon}
            size={16}
            color={error ? danger : muted}
            style={{ marginRight: 8 }}
          />
        )}

        {/* Selected value / Placeholder */}
        <Text
          style={{
            flex: 1,
            fontSize: FONT_SIZE,
            color: selectedOption ? textColor : error ? danger : muted,
          }}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {selectedOption ? selectedOption.label : placeholder}
        </Text>

        {/* Dropdown arrow */}
        <Icon name={ChevronDown} size={16} color={error ? danger : muted} />
      </Pressable>

      {/* Error */}
      {error && (
        <Text
          style={[
            { marginLeft: 14, marginTop: 4, fontSize: 14, color: danger },
            errorStyle,
          ]}
        >
          {error}
        </Text>
      )}

      {/* Modal with options */}
      <Modal
        visible={open}
        transparent
        animationType="fade"
        onRequestClose={() => setOpen(false)}
      >
        <Pressable
          style={[
            {
              flex: 1,
              backgroundColor: "#00000066",
              justifyContent: "center",
              padding: 20,
            },
            modalOverlayStyle,
          ]}
          onPress={() => setOpen(false)}
        >
          <View
            style={[
              {
                backgroundColor: isDark ? background : secondaryForeground,
                borderRadius: BORDER_RADIUS,
                maxHeight: "60%",
              },
              modalContentStyle,
            ]}
          >
            <FlatList
              data={options}
              keyExtractor={(item) => item.value.toString()}
              ItemSeparatorComponent={() => <Separator />}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => handleSelect(item.value)}
                  style={{
                    paddingVertical: 14,
                    paddingHorizontal: 16,
                  }}
                >
                  <Text
                    style={[
                      {
                        fontSize: FONT_SIZE,
                        color: item.value === value ? primary : textColor,
                      },
                      optionTextStyle, 
                    ]}
                  >
                    {item.label}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};
