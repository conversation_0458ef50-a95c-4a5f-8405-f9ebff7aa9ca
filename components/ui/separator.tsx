import { Text } from '@/components/ui/text';
import { View } from '@/components/ui/view';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { ViewStyle } from 'react-native';

interface SeparatorProps {
  orientation?: 'horizontal' | 'vertical';
  style?: ViewStyle;
  label?: string;
  labelPosition?: 'left' | 'center' | 'right';
}

export function Separator({
  orientation = 'horizontal',
  style,
  label,
  labelPosition = 'center',
}: SeparatorProps) {
  const borderColor = useThemeColor({}, 'border');

  if (label && orientation === 'horizontal') {
    return (
      <View
        style={[{ flexDirection: 'row', alignItems: 'center', gap: 8 }, style]}
      >
        {(labelPosition === 'center' || labelPosition === 'right') && (
          <View
            style={{ flex: 1, height: 1, backgroundColor: borderColor }}
          />
        )}
        <Text>{label}</Text>
        {(labelPosition === 'center' || labelPosition === 'left') && (
          <View
            style={{ flex: 1, height: 1, backgroundColor: borderColor }}
          />
        )}
      </View>
    );
  }

  return (
    <View
      style={[
        {
          backgroundColor: borderColor,
          ...(orientation === 'horizontal'
            ? { height: 1, width: '100%' }
            : { width: 1, height: '100%' }),
        },
        style,
      ]}
    />
  );
}
