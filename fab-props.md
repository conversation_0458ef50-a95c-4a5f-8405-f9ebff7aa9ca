
# FAB Component Props

This document provides a detailed explanation of the props available for the `FAB` component in `components/ui/fab.tsx`.

## `FABProps` Interface

### Core Props

- **`icon`**: `React.ReactNode` (Required)
  - **Description**: The main icon to be displayed within the Floating Action Button.
  - **Usage**:
    ```tsx
    <FAB icon={<Icon name="add" />} />
    ```

- **`label`**: `string` (Optional)
  - **Description**: A text label for the FAB. The display of this label depends on the `variant` and `extended` props.
  - **Usage**:
    ```tsx
    <FAB icon={<Icon name="add" />} label="Create" />
    ```

- **`onPress`**: `() => void` (Required)
  - **Description**: A callback function that is executed when the user presses the FAB. If `actions` are provided, this will toggle the speed-dial menu.
  - **Usage**:
    ```tsx
    <FAB icon={<Icon name="add" />} onPress={() => console.log("FAB pressed!")} />
    ```

### Speed-Dial Actions

- **`actions`**: `FABAction[]` (Optional)
  - **Description**: An array of action objects to create a speed-dial menu that appears when the FAB is pressed. Each action object has the following properties:
    - `icon`: `React.ReactNode` (Required) - The icon for the action button.
    - `label`: `string` (Optional) - A label for the action.
    - `onPress`: `() => void` (Required) - A callback for when the action is pressed.
    - `disabled`: `boolean` (Optional) - If `true`, the action is disabled.
    - `color`: `string` (Optional) - The background color of the action button.
    - `foregroundColor`: `string` (Optional) - The color of the action icon.
  - **Usage**:
    ```tsx
    <FAB
      icon={<Icon name="add" />}
      actions={[
        { icon: <Icon name="document" />, label: "New Doc", onPress: () => {} },
        { icon: <Icon name="image" />, label: "New Image", onPress: () => {} },
      ]}
    />
    ```

- **`open`**: `boolean` (Optional)
  - **Description**: A controlled prop to manage the open/closed state of the speed-dial menu.
  - **Usage**:
    ```tsx
    const [isOpen, setIsOpen] = useState(false);
    <FAB ... open={isOpen} onOpenChange={setIsOpen} />
    ```

- **`onOpenChange`**: `(open: boolean) => void` (Optional)
  - **Description**: A callback function that is invoked when the speed-dial menu's open state changes.
  - **Usage**:
    ```tsx
    <FAB ... onOpenChange={(isOpen) => console.log(isOpen)} />
    ```

### Positioning and Layout

- **`position`**: `"bottom-right" | "bottom-left" | "bottom-center"` (Optional, default: `"bottom-right"`)
  - **Description**: Determines the position of the FAB on the screen.
  - **Usage**:
    ```tsx
    <FAB ... position="bottom-left" />
    ```

- **`offset`**: `{ x?: number; y?: number }` (Optional, default: `{ x: 24, y: 24 }`)
  - **Description**: An object to specify the horizontal and vertical offset from the screen edges.
  - **Usage**:
    ```tsx
    <FAB ... offset={{ x: 16, y: 16 }} />
    ```

### Styling and Appearance

- **`color`**: `string` (Optional, default: `"#e4e4e4ff"`)
  - **Description**: The background color of the main FAB.
  - **Usage**:
    ```tsx
    <FAB ... color="blue" />
    ```

- **`foregroundColor`**: `string` (Optional, default: `"#111"`)
  - **Description**: The color of the FAB's icon and label.
  - **Usage**:
    ```tsx
    <FAB ... foregroundColor="white" />
    ```

- **`size`**: `"small" | "medium" | "large"` (Optional, default: `"medium"`)
  - **Description**: The size of the main FAB.
  - **Usage**:
    ```tsx
    <FAB ... size="large" />
    ```

- **`shape`**: `"circular" | "rounded"` (Optional, default: `"circular"`)
  - **Description**: The shape of the FAB. `"circular"` gives a perfect circle, while `"rounded"` gives a rounded rectangle.
  - **Usage**:
    ```tsx
    <FAB ... shape="rounded" />
    ```

- **`variant`**: `"standard" | "extended"` (Optional, default: `"standard"`)
  - **Description**: The variant of the FAB. `"extended"` shows the label next to the icon.
  - **Usage**:
    ```tsx
    <FAB ... variant="extended" label="Create" />
    ```

- **`extended`**: `boolean` (Optional)
  - **Description**: A convenience prop that overrides the `variant`. If `true`, the FAB will be `"extended"`.
  - **Usage**:
    ```tsx
    <FAB ... extended label="Create" />
    ```

- **`elevation`**: `"elevated" | "flat"` (Optional, default: `"elevated"`)
  - **Description**: The elevation style of the FAB, controlling the shadow.
  - **Usage**:
    ```tsx
    <FAB ... elevation="flat" />
    ```

- **`style`**: `StyleProp<ViewStyle>` (Optional)
  - **Description**: Custom styles to be applied to the FAB's container.
- **`iconStyle`**: `object` (Optional)
  - **Description**: Custom styles for the icon.
- **`labelStyle`**: `object` (Optional)
  - **Description**: Custom styles for the label.

### Animation and Visibility

- **`visible`**: `boolean` (Optional, default: `true`)
  - **Description**: Controls the visibility of the FAB.
  - **Usage**:
    ```tsx
    <FAB ... visible={false} />
    ```

- **`animationType`**: `"fade" | "scale" | "slide"` (Optional, default: `"scale"`)
  - **Description**: The animation type for showing and hiding the FAB.
  - **Usage**:
    ```tsx
    <FAB ... animationType="slide" />
    ```

- **`animationDuration`**: `number` (Optional, default: `200`)
  - **Description**: The duration of the show/hide animation in milliseconds.
  - **Usage**:
    ```tsx
    <FAB ... animationDuration={500} />
    ```

### State

- **`disabled`**: `boolean` (Optional, default: `false`)
  - **Description**: If `true`, the FAB is not pressable and has a reduced opacity.
  - **Usage**:
    ```tsx
    <FAB ... disabled />
    ```

- **`loading`**: `boolean` (Optional, default: `false`)
  - **Description**: If `true`, a spinner is shown instead of the icon.
  - **Usage**:
    ```tsx
    <FAB ... loading />
    ```

### Advanced Customization

- **`overlayColor`**: `string` (Optional, default: `"rgba(0,0,0,0.2)"`)
  - **Description**: The color of the overlay shown behind the speed-dial actions.
- **`overlayOpacity`**: `number` (Optional, default: `1`)
  - **Description**: The opacity of the overlay (0 to 1).
- **`overlayOpacityPercent`**: `number` (Optional)
  - **Description**: The opacity of the overlay as a percentage (0 to 100).
- **`overlayTintColor`**: `string` (Optional)
  - **Description**: A solid tint color for the overlay.
- **`extendedLabelPosition`**: `"left" | "right"` (Optional, default: `"right"`)
  - **Description**: The position of the label relative to the icon in the `"extended"` variant.
- **`actionsLabelPosition`**: `"left" | "right" | "auto"` (Optional, default: `"auto"`)
  - **Description**: The position of the labels for the speed-dial actions.
- **`actionLabelColor`**: `string` (Optional)
  - **Description**: The text color for the action labels.
- **`actionSize`**: `"small" | "medium" | "large" | number` (Optional)
  - **Description**: The size of the speed-dial action buttons.

### Scroll Behavior

- **`collapseOnScroll`**: `boolean` (Optional, default: `false`)
  - **Description**: If `true`, the FAB will collapse to its standard size when scrolling down.
- **`scrollY`**: `SharedValue<number>` (Optional)
  - **Description**: A `react-native-reanimated` shared value representing the scroll position of a scrollable view.
- **`scrollThreshold`**: `number` (Optional, default: `50`)
  - **Description**: The scroll distance required to trigger the collapse animation.
