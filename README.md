# SACCO Field Agent App

![Status](https://img.shields.io/badge/status-work_in_progress-yellow.svg)

## 📖 Overview

This project is a mobile application designed for SACCO (Savings and Credit Cooperative Organization) field agents. The goal is to provide a tool that helps agents manage member information, track financial transactions, and organize their daily tasks efficiently while in the field.

This application is currently under active development.

## ✨ Features

The application is planned to have the following features:

*   **Dashboard:** A summary of key metrics and recent activities.
*   **Member Management:** View, search, and manage SACCO member profiles.
*   **Transaction Logging:** Record and view member deposits, withdrawals, and loan payments.
*   **Task Management:** A list of daily tasks and assignments for the field agent.
*   **Dark/Light Mode:** A themeable interface for better usability in different lighting conditions.

## 🚀 Technologies Used

*   **Framework:** [React Native](https://reactnative.dev/) via [Expo](https://expo.dev/)
*   **Language:** [TypeScript](https://www.typescriptlang.org/)
*   **Routing:** [Expo Router](https://expo.github.io/router/)
*   **Styling:** Custom UI components with support for light/dark modes.
*   **Icons:** [Lucide React Native](https://lucide.dev/)
*   **Linting:** [ESLint](https://eslint.org/)

## 🏁 Getting Started

Follow these instructions to get a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

You will need [Node.js](https://nodejs.org/) and [npm](https://www.npmjs.com/) installed on your machine. You will also need the Expo Go app on your mobile device or an emulator set up.

### Installation

1.  Clone the repository:
    ```bash
    git clone <repository-url>
    ```
2.  Navigate to the project directory:
    ```bash
    cd sacco-field-agent-app
    ```
3.  Install the dependencies:
    ```bash
    npm install
    ```

### Running the Application

Once the dependencies are installed, you can run the application using the following scripts:

```bash
# Start the development server
npm start

# Run on Android
npm run android

# Run on iOS
npm run ios

# Run on Web
npm run web
```

This will start the Metro bundler. You can then scan the QR code with the Expo Go app on your phone or run it on an emulator.

## 🤝 Contributing

This is a work-in-progress project. Contributions, issues, and feature requests are welcome. Please feel free to check the issues page.

---
