import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useLocalSearchParams } from 'expo-router';

const memberProfile = () => {
  const { id } = useLocalSearchParams();
  return (
    
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Text>Member Profile {id}</Text>
    </View>
  )
}

export default memberProfile

const styles = StyleSheet.create({})