import { useEffect, useMemo, useState } from "react";
import { FlatList, Pressable, StyleSheet, TouchableOpacity } from "react-native";
import { Image } from "@/components/ui/image";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Icon } from "@/components/ui/icon";
import { ScrollView } from "@/components/ui/scroll-view";
import { Input } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import { BORDER_RADIUS, FONT_SIZE, CORNERS } from "@/theme/globals";
import { ChevronDown, Filter, Plus, Search } from "lucide-react-native";

import { fetchMembers, timeAgo, type Member } from "@/lib/membersApi";
import { Separator } from "@/components/ui/separator";
import { TouchableHighlight } from "react-native";
import { useRouter } from 'expo-router';

export default function Members() {
  const bottom = useBottomTabBarHeight();
  const router = useRouter();

  const cardColor = useThemeColor({}, "card");
  const borderColor = useThemeColor({}, "border");
  const muted = useThemeColor({}, "textMuted");

  const [query, setQuery] = useState("");
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);

  type StatusFilter = "all" | "active" | "overdue" | "new" | "high_balance";
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");

  useEffect(() => {
    let mounted = true;
    (async () => {
      const data = await fetchMembers();
      if (mounted) {
        setMembers(data);
        setLoading(false);
      }
    })();
    return () => {
      mounted = false;
    };
  }, []);

  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase();
    return members.filter((m) => {
      const matchesQuery =
        q.length === 0 ||
        m.name.toLowerCase().includes(q) ||
        m.memberId.toLowerCase().includes(q);
      const matchesStatus =
        statusFilter === "all" ? true : m.status === statusFilter;
      return matchesQuery && matchesStatus;
    });
  }, [members, query, statusFilter]);

  const renderStatusBadge = (status: Member["status"]) => {
    switch (status) {
      case "overdue":
        return (
          <Badge
            style={{ backgroundColor: "#f59e0b" }}
            textStyle={{ color: "white", fontWeight: "600", fontSize: 11 }}
            variant="destructive"
          >
            Overdue
          </Badge>
        );
      case "new":
        return (
          <Badge
            style={{ backgroundColor: "#3b82f6" }}
            textStyle={{ color: "white", fontSize: 11 }}
            variant="secondary"
          >
            New
          </Badge>
        );
      case "high_balance":
        return (
          <Badge textStyle={{ fontSize: 11 }} variant="success">
            High Balance
          </Badge>
        );
      case "active":
      default:
        return null;
    }
  };

  const renderItem = ({ item }: { item: Member }) => {
    return (
      <TouchableHighlight
        underlayColor={cardColor}
        onPress={() => router.push(`/members/${item.memberId}`)}
      >
      <Card style={styles.card}>
        <View style={styles.row}>
          <Image
            source={{ uri: item.avatar }}
            variant="circle"
            width={48}
            height={48}
            loadingIndicatorSize="small"
            errorFallbackText="No Avatar"
          />

          <View style={styles.main}>
            <Text style={{ fontSize: 18 }} variant="subtitle" numberOfLines={1}>
              {item.name}
            </Text>
            <Text style={{ fontSize: 14 }} variant="caption">
              Member ID: {item.memberId}
            </Text>
          </View>

          <View style={styles.meta}>
            <Text variant="caption" style={{ color: muted, fontSize: 11 }}>
              {timeAgo(item.lastActive)}
            </Text>
            {renderStatusBadge(item.status)}
          </View>
        </View>
      </Card>
      </TouchableHighlight>
    );
  };

  return (
    <View style={[styles.container]}>
      {/* Top Bar */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text variant="title">Members</Text>

          <TouchableOpacity
            accessibilityLabel="Add member"
            style={styles.iconBtn}
          >
            <Icon name={Plus} size={22} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search */}
      <View style={styles.searchSection}>
        <View >
          <Input
            placeholder="Search by name or ID"
            icon={Search}
            value={query}
            onChangeText={setQuery}
            placeholderTextColor={muted}
            // inputStyle={styles.input}
            variant="filled"
            disabled={loading}
            inputContainerStyle={{
              borderWidth: 2,
              borderRadius: BORDER_RADIUS,
            }}
          />
        </View>

        {/* Filters */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.filters}
          contentContainerStyle={styles.filtersContent}
        >
          {/* <Button
            size="sm"
            variant="outline"
            icon={Filter}
            onPress={() => setStatusFilter("all")}
          >
            Filters
          </Button> */}

          <FilterChip
            label="Active"
            selected={statusFilter === "active"}
            onPress={() =>
              setStatusFilter(statusFilter === "active" ? "all" : "active")
            }
            
          />
          <FilterChip
            label="Overdue"
            selected={statusFilter === "overdue"}
            onPress={() =>
              setStatusFilter(statusFilter === "overdue" ? "all" : "overdue")
            }
          />

          <FilterChip
            label="New"
            selected={statusFilter === "new"}
            onPress={() =>
              setStatusFilter(statusFilter === "new" ? "all" : "new")
            }
          />
          <FilterChip
            label="High Balance"
            selected={statusFilter === "high_balance"}
            onPress={() =>
              setStatusFilter(
                statusFilter === "high_balance" ? "all" : "high_balance"
              )
            }
          />
        </ScrollView>
      </View>
      <View style={{ paddingHorizontal: 0 }}>
        <Separator />
      </View>

      {/* List Section */}
      <View style={[styles.section]}>
        <FlatList
          data={filtered}
          keyExtractor={(m) => m.id}
          renderItem={renderItem}
          contentContainerStyle={styles.listContent}
          ItemSeparatorComponent={() => (
            <Separator style={{ marginVertical: 8 }} />
          )}
          ListEmptyComponent={
            loading ? (
              <Text style={{ textAlign: "center", color: muted }}>
                Loading...
              </Text>
            ) : (
              <Text style={{ textAlign: "center", color: muted }}>
                No members found
              </Text>
            )
          }
        />
      </View>
    </View>
  );
}

function FilterChip({
  label,
  selected,
  onPress,
  rightIcon,
}: {
  label: string;
  selected?: boolean;
  onPress?: () => void;
  rightIcon?: React.ComponentType<any>;
}) {
  const muted = useThemeColor({}, "muted");
  const primary = useThemeColor({}, "primary");
  return (
    <TouchableOpacity onPress={onPress}>
      <Badge
        variant={selected ? "secondary" : "outline"}
        style={{
          borderWidth: 1,
          borderColor: selected ? primary : muted,
          borderRadius: CORNERS,
        }}
      >
        <View style={{ flexDirection: "row", alignItems: "center", gap: 6 }}>
          <Text variant="caption" style={{ fontSize: 12 }}>{label}</Text>
          {rightIcon ? <Icon name={rightIcon} size={16} /> : null}
        </View>
      </Badge>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 64,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  iconBtn: {
    height: 36,
    width: 36,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 10,
  },
  searchSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },


  input: {
    flex: 1,
    padding: 0,
    fontSize: FONT_SIZE,
  },
  filters: {
    marginTop: 16,
  },
  filtersContent: {
    gap: 8,
  },
  section: {
    flex: 1,
    paddingHorizontal: 16,
  },
  listContent: {
    paddingBottom: 24,
    paddingTop: 10,
  },
  card: {
    padding: 12,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  main: {
    flex: 1,
    gap: 2,
  },
  meta: {
    alignItems: "flex-end",
    gap: 8,
  },
});
