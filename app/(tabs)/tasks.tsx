import React from "react";
import { Dimensions, StyleSheet } from "react-native";
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import { Calendar } from "react-native-calendars";
import {
  ChevronLeft,
  ChevronRight,
  Plus,
  ShieldAlert,
} from "lucide-react-native";

import { Carousel, CarouselItem } from "@/components/ui/carousel";
import FAB from "@/components/ui/fab";
import { Icon } from "@/components/ui/icon";
import { ScrollView } from "@/components/ui/scroll-view";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useModeToggle } from "@/hooks/useModeToggle";

const { width: screenWidth } = Dimensions.get("window");

type Task = {
  id: number;
  name: string;
  price: string;
  rating: string;
};

const tasks: Task[] = [
  { id: 1, name: "Wireless Headphones", price: "$99.99", rating: "4.8" },
  { id: 2, name: "Smart Watch", price: "$199.99", rating: "4.9" },
  { id: 3, name: "Bluetooth Speaker", price: "$79.99", rating: "4.7" },
  { id: 4, name: "Phone Case", price: "$24.99", rating: "4.6" },
  { id: 5, name: "Wireless Charger", price: "$39.99", rating: "4.8" },
];

const TaskCard = ({
  task,
  cardColor,
  textColor,
}: {
  task: Task;
  cardColor: string;
  textColor: string;
}) => (
  <CarouselItem style={[styles.taskCard, { backgroundColor: cardColor }]}>
    <View style={styles.taskCardInner}>
      <View>
        <Text variant="title" style={[styles.taskTitle, { color: textColor }]}>
          {task.name}
        </Text>
        <Text style={[styles.taskRating, { color: textColor }]}>
          ⭐ {task.rating} rating
        </Text>
      </View>
      <Text variant="title" style={styles.taskPrice}>
        {task.price}
      </Text>
    </View>
  </CarouselItem>
);

const TaskCarousel = ({
  title,
  icon,
  data,
  cardColor,
  textColor,
}: {
  title: string;
  icon: React.ComponentType<any>;
  data: Task[];
  cardColor: string;
  textColor: string;
}) => (
  <View>
    <View style={styles.sectionHeader}>
      <Text variant="subtitle">{title}</Text>
      <Icon name={icon} size={25} />
    </View>

    <Carousel itemWidth={screenWidth * 0.7} spacing={0}>
      {data.map((task) => (
        <TaskCard
          key={task.id}
          task={task}
          cardColor={cardColor}
          textColor={textColor}
        />
      ))}
    </Carousel>
  </View>
);

export default function TasksScreen() {
  const bottom = useBottomTabBarHeight();
  const { toggleMode, isDark } = useModeToggle();

  const background = useThemeColor({}, "background");
  const cardColor = useThemeColor({}, "card");
  const textColor = useThemeColor({}, "text");
  const primaryColor = useThemeColor({}, "primary");
  const muted = useThemeColor({}, "muted");
  const secondaryForeground = useThemeColor({}, "secondaryForeground");

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text variant="title">Todo's</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ paddingBottom: bottom + 20 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Today’s Tasks */}
        <TaskCarousel
          title="Todo's Today"
          icon={ShieldAlert}
          data={tasks}
          cardColor={cardColor}
          textColor={textColor}
        />

        {/* Schedule */}
        <View style={{ marginTop: 20 }}>
          <Text variant="subtitle">Schedule</Text>
          <Calendar
            enableSwipeMonths
            style={styles.calendar}
            hideExtraDays={true}
            markedDates={{
              "2025-10-20": { textColor: "green" },
              "2025-10-22": { startingDay: true, color: "green" },
              "2025-10-23": {
                selected: true,
                endingDay: true,
                color: "green",
                textColor: "gray",
              },
              "2025-10-04": {
                disabled: true,
                startingDay: true,
                color: "green",
                endingDay: true,
              },
            }}
            theme={{
              calendarBackground: cardColor,
              textSectionTitleColor: textColor,
              selectedDayBackgroundColor: "orange",
              selectedDayTextColor: textColor,
              todayTextColor: textColor,
              todayBackgroundColor: primaryColor,
              dayTextColor: textColor,
              monthTextColor: textColor,
              textDayFontFamily: "Manrope_400Regular",
              textMonthFontFamily: "Manrope_400Regular",
              textDayHeaderFontFamily: "Manrope_400Regular",
              arrowColor: muted,
            }}
            renderArrow={(direction) =>
              direction === "left" ? (
                <ChevronLeft size={20} color={muted} />
              ) : (
                <ChevronRight size={20} color={muted} />
              )
            }
          />
        </View>

        {/* Urgent */}
        <TaskCarousel
          title="Urgent"
          icon={ShieldAlert}
          data={tasks}
          cardColor={cardColor}
          textColor={textColor}
        />
      </ScrollView>

      {/* Floating Action Button */}
      <FAB
        icon={<Plus size={24} />}
        position="bottom-right"
        label="New Task"
        variant="extended"
        elevation="elevated"
        collapseOnScroll
        overlayOpacityPercent={90}
        actionLabelColor="#fff"
        overlayTintColor={isDark ? background : secondaryForeground}
        scrollThreshold={10}
        onPress={() => console.log("FAB Pressed")}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, paddingHorizontal: 20 },
  scrollView: { flex: 1 },
  header: { paddingTop: 64, paddingBottom: 20 },
  sectionHeader: {
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 20,
  },
  taskCard: { minHeight: 160, padding: 20, borderRadius: 12 },
  taskCardInner: { flex: 1, justifyContent: "space-between" },
  taskTitle: { fontSize: 18, marginBottom: 8 },
  taskRating: { opacity: 0.7, marginBottom: 12 },
  taskPrice: { color: "#10b981", fontSize: 20, fontWeight: "bold" },
  calendar: { borderRadius: 12, marginTop: 10, height: 360 },
});
