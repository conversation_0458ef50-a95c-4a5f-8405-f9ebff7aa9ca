import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Icon } from "@/components/ui/icon";
import { ScrollView } from "@/components/ui/scroll-view";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import {
  Building2,
  ChevronRight,
  User,
  Database,
  CheckCircle,
  HelpCircle,
  Info,
  FileText,
  Shield,
  LogOut,
  Headset,
} from "lucide-react-native";
import { Separator } from "@/components/ui/separator";

type MenuItemProps = {
  label: string;
  icon: React.ComponentType<any>;
  onPress?: () => void;
};

const MenuItem = ({ label, icon: IconName, onPress }: MenuItemProps) => (
  <TouchableOpacity onPress={onPress}>
    <Card style={styles.card}>
      <CardContent>
        <View style={styles.rowBetween}>
          <View style={styles.row}>
            <Icon name={IconName} size={22} />
            <Text variant="body">{label}</Text>
          </View>
          <Icon name={ChevronRight} size={22} />
        </View>
      </CardContent>
    </Card>
  </TouchableOpacity>
);

export default function MenuScreen() {
  const bottom = useBottomTabBarHeight();

  const sections = [
    {
      title: "Account",
      icon: Building2,
      items: [
        { label: "Settings", icon: User },
        { label: "Sync & Storage", icon: Database },
        { label: "Approvals", icon: CheckCircle },
      ],
    },
    {
      title: "Support",
      icon: Headset,
      items: [{ label: "Help & Support", icon: HelpCircle }],
    },
    {
      title: "App Info",
      icon: Info,
      items: [
        { label: "About & Version", icon: Info },
        { label: "Terms & Conditions", icon: FileText },
        { label: "Privacy Policy", icon: Shield },
      ],
    },
  ];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text variant="title">Menu</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ paddingBottom: bottom + 20 }}
        showsVerticalScrollIndicator={false}
      >
        {sections.map((section, idx) => (
          <View key={idx} style={{ marginBottom: 20 }}>
            <View style={styles.accountHeadingContainer}>
              <Text variant="subtitle">{section.title}</Text>

              <Icon name={section.icon} size={25} />
            </View>
            <Separator style={{ marginVertical: 10 }} />
            {section.items.map((item, i) => (
              <MenuItem key={i} {...item} />
            ))}
          </View>
        ))}

        {/* Logout (separate) */}
        <Separator style={{ marginVertical: 16 }} />
        <MenuItem label="Log Out" icon={LogOut} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingTop: 64,
    paddingBottom: 20,
  },
  accountHeadingContainer: {
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  card: {
    padding: 16,
    marginBottom: 10,
    elevation: 0,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  rowBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
});
