import { Icon } from "@/components/ui/icon";
import { PlatformPressable } from "@react-navigation/elements";
import * as Haptics from "expo-haptics";
import { Tabs } from "expo-router";
import { LayoutDashboard, UsersRound, ArrowLeftRight, Menu, CircleCheckBig, Plus } from "lucide-react-native";
import React, { useEffect, useRef, useState } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { StyleSheet, View, Dimensions } from "react-native";
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  FadeInRight,

} from "react-native-reanimated";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useModeToggle } from "@/hooks/useModeToggle";
import FAB from "@/components/ui/fab";

interface TabInfo {
  name: string;
  label: string;
  icon: any;
}

const tabs: TabInfo[] = [
  { name: "index", label: "Dash", icon: LayoutDashboard },
  { name: "members", label: "Members", icon: UsersRound },
  { name: "transactions", label: "TXNs", icon: ArrowLeftRight },
  { name: "tasks", label: "Todos", icon: CircleCheckBig },
  { name: "menu", label: "More", icon: Menu },
];

function SlidingTabBar({
  activeIndex,
  onTabPress,
}: {
  activeIndex: number;
  onTabPress: (index: number) => void;
}) {
  const insets = useSafeAreaInsets();
  const { toggleMode, isDark } = useModeToggle();

  // Theme colors
  const primaryColor = useThemeColor({}, "primary");
  const mutedColor = useThemeColor({}, "textMuted");
  const primaryForeground = useThemeColor({}, "primaryForeground");
  const secondary = useThemeColor({}, "secondary");
  const secondaryForeground = useThemeColor({}, "secondaryForeground");
  const greenColor = useThemeColor({}, "green");
  const redColor = useThemeColor({}, "red");
  const muted = useThemeColor({}, "muted");
  const baseHeight = 80;

  const pillX = useSharedValue(0);
  const pillWidth = useSharedValue(80);
  
  // Animated values for each tab's position and width
  const tabPositions = tabs.map(() => useSharedValue(0));
  const tabWidths = tabs.map(() => useSharedValue(0));

  const screenWidth = Dimensions.get('window').width;
  const paddingHorizontal = 20;
  const availableWidth = screenWidth - (paddingHorizontal * 2);

  // Calculate pill width based on text length
  const getPillWidth = (label: string) => {
    const iconWidth = 20;
    const textWidth = label.length * 8.2;
    const gap = 6;
    const padding = 32;
    return iconWidth + textWidth + gap + padding;
  };

  // Calculate dynamic tab widths and positions
  const calculateTabLayout = (activeIdx: number) => {
    const activePillWidth = getPillWidth(tabs[activeIdx].label);
    const inactiveTabWidth = 44; // Minimum width for icon only
    const minGap = 4; // Minimum gap between tabs
    
    // Calculate total width needed
    const totalInactiveWidth = (tabs.length - 1) * inactiveTabWidth;
    const totalGaps = (tabs.length - 1) * minGap;
    const remainingSpace = availableWidth - activePillWidth - totalInactiveWidth - totalGaps;
    
    // Distribute remaining space as additional gap
    const additionalGapPerTab = Math.max(0, remainingSpace / (tabs.length - 1));
    const gap = minGap + additionalGapPerTab;
    
    let currentX = 0;
    const positions: number[] = [];
    const widths: number[] = [];
    
    for (let i = 0; i < tabs.length; i++) {
      if (i === activeIdx) {
        positions.push(currentX);
        widths.push(activePillWidth);
        currentX += activePillWidth + gap;
      } else {
        positions.push(currentX);
        widths.push(inactiveTabWidth);
        currentX += inactiveTabWidth + gap;
      }
    }
    
    return { positions, widths };
  };

  // Animate tab positions when active index changes
  useEffect(() => {
    const { positions, widths } = calculateTabLayout(activeIndex);
    
    // Animate each tab to its new position
    positions.forEach((pos, idx) => {
      tabPositions[idx].value = withTiming(pos, {
        duration: 350,
        easing: Easing.out(Easing.cubic),
      });
      tabWidths[idx].value = withTiming(widths[idx], {
        duration: 350,
        easing: Easing.out(Easing.cubic),
      });
    });
    
    // Animate pill
    const pillW = getPillWidth(tabs[activeIndex].label);
    pillWidth.value = withTiming(pillW, {
      duration: 350,
      easing: Easing.out(Easing.cubic),
    });
    pillX.value = withTiming(positions[activeIndex], {
      duration: 350,
      easing: Easing.out(Easing.cubic),
    });
  }, [activeIndex]);

  const pillStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: pillX.value }],
    width: pillWidth.value,
    height: 40,
    backgroundColor: isDark ? secondary : primaryForeground,
    borderRadius: 20,
    borderColor: muted,
    borderWidth: 1,
    position: "absolute",
    top: 16,
    left: paddingHorizontal,
  }));

  return (
    <View
      style={[
        styles.tabBar,
        {
          height: baseHeight + insets.bottom,
          paddingBottom: insets.bottom + 8,
        },
      ]}
    >
      {/* Sliding pill */}
      <Animated.View style={pillStyle} />

      {/* Tabs container */}
      <View style={styles.tabContainer}>
        {tabs.map((tab, index) => {
          const isActive = index === activeIndex;
          
          const animatedTabStyle = useAnimatedStyle(() => ({
            transform: [{ translateX: tabPositions[index].value }],
            width: tabWidths[index].value,
            position: 'absolute',
            left: 0,
            top: 0,
            height: 40,
          }));

          return (
            <Animated.View key={tab.name} style={animatedTabStyle}>
              <PlatformPressable
                onPress={() => {
                  if (process.env.EXPO_OS === "ios") {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }
                  onTabPress(index);
                }}
                style={styles.tabPressable}
              >
                <View
                  style={[
                    styles.tabContent,
                    isActive && { flexDirection: "row", gap: 6 },
                  ]}
                >
                  <Icon
                    name={tab.icon}
                    size={20}
                    color={isActive && isDark ? secondaryForeground : mutedColor}
                  />
                  {isActive && (
                    <Animated.Text
                      entering={FadeInRight.duration(1000).easing(Easing.out(Easing.cubic))}
                      style={[
                        styles.pillLabel,
                        {
                          color: isActive && isDark ? secondaryForeground : mutedColor,
                        },
                      ]}
                    >
                      {tab.label}
                    </Animated.Text>
                  )}
                </View>
              </PlatformPressable>
            </Animated.View>
          );
        })}
        
      </View>
      
    </View>
  );
}

export default function TabLayout() {
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: { display: "none" }, // Hide default tab bar
      }}
      tabBar={(props) => {
        const currentIndex = tabs.findIndex(
          (tab) => tab.name === props.state.routes[props.state.index]?.name
        );
        if (currentIndex !== -1 && currentIndex !== activeTabIndex) {
          setActiveTabIndex(currentIndex);
        }

        return (
          <SlidingTabBar
            activeIndex={activeTabIndex}
            onTabPress={(index) => {
              setActiveTabIndex(index);
              props.navigation.navigate(tabs[index].name);
            }}
          />
        );
      }}
    >
      
      <Tabs.Screen name="index" options={{ title: "Dash" }} />
      <Tabs.Screen name="members" options={{ title: "Members" }} />
      <Tabs.Screen name="transactions" options={{ title: "Transactions" }} />
      <Tabs.Screen name="tasks" options={{ title: "Tasks" }} />
      <Tabs.Screen name="menu" options={{ title: "More" }} />
     
    </Tabs>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    borderTopWidth: 0,
    paddingTop: 16,
    paddingHorizontal: 20,
    position: "relative",
  },
  tabContainer: {
    height: 40,
    position: 'relative',
  },
  tabPressable: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  tabContent: {
    alignItems: "center",
    justifyContent: "center",
  },
  pillLabel: {
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: 0.2,
  },
});