import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { StyleSheet } from "react-native";
import { Image } from "@/components/ui/image";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollView } from "@/components/ui/scroll-view";
import { Input } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import { DatePicker } from "@/components/ui/date-picker";
import { BORDER_RADIUS } from "@/theme/globals";
import { User } from "lucide-react-native";
import { useThemeColor } from "@/hooks/useThemeColor";
import { Separator } from "@/components/ui/separator";
import { SearchBarWithSuggestions } from "@/components/ui/searchbar";
import { Select } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { fetchMembers, timeAgo, type Member } from "@/lib/membersApi";
import { useModeToggle } from "@/hooks/useModeToggle";

// Form field configuration for better maintainability
const CONTRIBUTION_FIELDS = [
  { key: "savings", label: "Savings" },
  { key: "deposit", label: "Deposit" },
  { key: "loanPayment", label: "Loan Payment" },
  { key: "loanInterest", label: "Loan Interest" },
  { key: "maintenanceFee", label: "Maintenance Fee" },
  { key: "lateCharges", label: "Late Charges" },
  { key: "loanForm", label: "Loan Form" },
  { key: "registrationFee", label: "Registration Fee" },
  { key: "passbook", label: "Passbook" },
] as const;

const STAFF_OPTIONS = [
  { label: "John Mwangi", value: "10012345" },
  { label: "Mary Achieng", value: "10023456" },
  { label: "Peter Kiptoo", value: "10034567" },
  { label: "Grace Wanjiku", value: "10045678" },
  { label: "David Odhiambo", value: "10056789" },
  { label: "Esther Njeri", value: "10067890" },
  { label: "Samuel Otieno", value: "10078901" },
  { label: "Lucy Chebet", value: "10089012" },
  { label: "James Kariuki", value: "10090123" },
  { label: "Agnes Wambui", value: "10101234" },
];

export default function Transactions() {
  const { isDark } = useModeToggle();

  // Theme colors - memoized
  const cardColor = useThemeColor({}, "card");
  const borderColor = useThemeColor({}, "border");
  const muted = useThemeColor({}, "textMuted");
  const background = useThemeColor({}, "background");
  const secondaryForeground = useThemeColor({}, "secondaryForeground");

  // Consolidated state
  const [query, setQuery] = useState("");
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMember, setSelectedMember] = useState<Member | undefined>();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [receivedBy, setReceivedBy] = useState<string | number>();
  const [sentViaPaybill, setSentViaPaybill] = useState(false);
  
  // Form values state
  const [formValues, setFormValues] = useState({
    amountReceived: "",
    savings: "",
    deposit: "",
    loanPayment: "",
    loanInterest: "",
    maintenanceFee: "",
    lateCharges: "",
    loanForm: "",
    registrationFee: "",
    passbook: "",
  });

  // Fetch members on mount
  useEffect(() => {
    let mounted = true;
    (async () => {
      const data = await fetchMembers();
      if (mounted) {
        setMembers(data);
        setLoading(false);
      }
    })();
    return () => {
      mounted = false;
    };
  }, []);

  // Memoized suggestions with better performance
  const suggestions = useMemo(() => {
    const seen = new Set<string>();
    const items: string[] = [];
    for (const m of members) {
      if (!seen.has(m.memberId)) {
        seen.add(m.memberId);
        items.push(`${m.name} - (${m.memberId})`);
      }
    }
    return items;
  }, [members]);

  // Memoized common input styles
  const inputStyles = useMemo(
    () => ({
      labelStyle: { fontSize: 14 },
      inputContainerStyle: {
        borderWidth: 2,
        borderRadius: BORDER_RADIUS,
      },
    }),
    []
  );

  // Callbacks for better performance
  const handleChangeText = useCallback((text: string) => {
    setQuery(text);
    setSelectedMember(undefined);
  }, []);

  const handleSuggestionPress = useCallback(
    (suggestion: string) => {
      const match = suggestion.match(/\(([^)]+)\)$/);
      const memberId = match ? match[1] : suggestion.trim();
      const found = members.find((m) => m.memberId === memberId);
      if (found) {
        setSelectedMember(found);
        setQuery(`${found.name} (${found.memberId})`);
      } else {
        setSelectedMember(undefined);
      }
    },
    [members]
  );

  const handleClear = useCallback(() => {
    setQuery("");
    setSelectedMember(undefined);
  }, []);

  const handleFormValueChange = useCallback((key: string, value: string) => {
    setFormValues((prev) => ({ ...prev, [key]: value }));
  }, []);

  const handleSubmit = useCallback(() => {
    // TODO: Implement submission logic
    console.log("Form submitted:", {
      member: selectedMember,
      date: selectedDate,
      receivedBy,
      sentViaPaybill,
      ...formValues,
    });
  }, [selectedMember, selectedDate, receivedBy, sentViaPaybill, formValues]);

  // Memoized status badge renderer
  const renderStatusBadge = useCallback((status: Member["status"]) => {
    switch (status) {
      case "overdue":
        return (
          <Badge
            style={styles.overdueBadge}
            textStyle={styles.badgeText}
            variant="destructive"
          >
            Overdue
          </Badge>
        );
      case "new":
        return (
          <Badge
            style={styles.newBadge}
            textStyle={styles.newBadgeText}
            variant="secondary"
          >
            New
          </Badge>
        );
      case "high_balance":
        return (
          <Badge textStyle={styles.badgeText} variant="success">
            High Balance
          </Badge>
        );
      default:
        return null;
    }
  }, []);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.mainHeader}>
        <Text variant="title">Transactions</Text>
      </View>

      {/* Member Information Section */}
      <View style={styles.container}>
        <View style={styles.searchSection}>
          <View style={styles.sectionHeader}>
            <Text variant="caption">Member Information</Text>
          </View>

          <View
            style={[
              styles.searchContainer,
              { borderColor, backgroundColor: cardColor },
            ]}
          >
            <SearchBarWithSuggestions
              placeholder="Search by name or ID"
              value={query}
              onChangeText={handleChangeText}
              onClear={handleClear}
              suggestions={suggestions}
              onSuggestionPress={handleSuggestionPress}
              maxSuggestions={8}
            />
          </View>

          {selectedMember && (
            <Card style={styles.memberCard}>
              <View style={styles.memberRow}>
                <Image
                  source={{ uri: selectedMember.avatar }}
                  variant="circle"
                  width={48}
                  height={48}
                  loadingIndicatorSize="small"
                  errorFallbackText="No Avatar"
                />

                <View style={styles.memberInfo}>
                  <Text style={styles.memberName} variant="subtitle" numberOfLines={1}>
                    {selectedMember.name}
                  </Text>
                  <Text style={styles.memberId} variant="caption">
                    Member ID: {selectedMember.memberId}
                  </Text>
                </View>

                <View style={styles.memberMeta}>
                  <Text variant="caption" style={[styles.lastActive, { color: muted }]}>
                    {timeAgo(selectedMember.lastActive)}
                  </Text>
                  {renderStatusBadge(selectedMember.status)}
                </View>
              </View>
            </Card>
          )}
        </View>

        <Separator style={styles.separator} />

        {/* Form Section */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Contribution Summary */}
          <View style={styles.formSection}>
            <View style={styles.sectionHeader}>
              <Text variant="caption">Contribution Summary</Text>
            </View>

            <View style={styles.formGroup}>
              <Input
                label="Amount Received"
                labelPosition="above"
                placeholder="0.00"
                startAdornment="KES "
                variant="filled"
                keyboardType="numeric"
                value={formValues.amountReceived}
                onChangeText={(value) => handleFormValueChange("amountReceived", value)}
                {...inputStyles}
              />
              <DatePicker
                label="Collection Date"
                labelPosition="above"
                mode="datetime"
                // error={!selectedDate ? undefined : "Collection date is required"}
                value={selectedDate}
                onChange={setSelectedDate}
                defaultToCurrentDate
                placeholder="Choose a date"
                variant="filled"
                style={inputStyles.inputContainerStyle}
                labelStyle={inputStyles.labelStyle}
          
              />
              <Select
                label="Received By"
                icon={User}
                placeholder="Select staff"
                options={STAFF_OPTIONS}
                value={receivedBy}
                onChange={setReceivedBy}
                selectContainerStyle={inputStyles.inputContainerStyle}
                labelStyle={inputStyles.labelStyle}
                modalOverlayStyle={{
                  backgroundColor: isDark ? background : secondaryForeground,
                }}
                optionTextStyle={styles.selectOption}
              />
            </View>
          </View>

          <Separator style={styles.separator} />

          {/* Contribution Details */}
          <View style={styles.formSection}>
            <View style={styles.sectionHeader}>
              <Text variant="caption">Contribution Details</Text>
            </View>

            <View style={styles.formGroup}>
              {CONTRIBUTION_FIELDS.map((field) => (
                <Input
                  key={field.key}
                  label={field.label}
                  labelPosition="above"
                  // error={!formValues[field.key as keyof typeof formValues] ? "Required" : undefined}
                  placeholder="0.00"
                  startAdornment="KES "
                  variant="filled"
                  keyboardType="numeric"
                  value={formValues[field.key as keyof typeof formValues]}
                  onChangeText={(value) => handleFormValueChange(field.key, value)}
                  {...inputStyles}
                />
              ))}

              <Checkbox
                checked={sentViaPaybill}
                onCheckedChange={setSentViaPaybill}
                label="Sent via paybill?"
              />
            </View>

            <Button
              style={styles.submitButton}
              size="lg"
              onPress={handleSubmit}
              disabled={!selectedMember}
            >
              Submit Transaction
            </Button>
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  mainHeader: {
    paddingTop: 64,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionHeader: {
    paddingVertical: 12,
    alignItems: "flex-end",
  },
  searchSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  searchContainer: {
    borderWidth: 1,
    borderRadius: BORDER_RADIUS,
  },
  memberCard: {
    padding: 12,
    marginTop: 12,
  },
  memberRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  memberInfo: {
    flex: 1,
    gap: 4,
  },
  memberName: {
    fontSize: 18,
  },
  memberId: {
    fontSize: 14,
  },
  memberMeta: {
    alignItems: "flex-end",
    gap: 8,
  },
  lastActive: {
    fontSize: 11,
  },
  separator: {
    marginVertical: 0,
  },
  formSection: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  formGroup: {
    gap: 12,
  },
  selectOption: {
    fontSize: 18,
    fontWeight: "600",
  },
  submitButton: {
    width: "100%",
    marginTop: 20,
  },
  // Badge styles
  overdueBadge: {
    backgroundColor: "#f59e0b",
  },
  newBadge: {
    backgroundColor: "#3b82f6",
  },
  badgeText: {
    color: "white",
    fontWeight: "600",
    fontSize: 11,
  },
  newBadgeText: {
    color: "white",
    fontSize: 11,
  },


});
