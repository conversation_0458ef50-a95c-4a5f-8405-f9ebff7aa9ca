import { View } from "@/components/ui/view";
import { Text } from "@/components/ui/text";
import { StyleSheet } from "react-native";
import React, { useMemo } from "react";
import { Mail, Lock, Fingerprint } from "lucide-react-native";
import { Input } from "@/components/ui/input";
import { BORDER_RADIUS } from "@/theme/globals";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useKeyboardHeight } from "@/hooks/useKeyboardHeight";

const login = () => {
  const primary = useThemeColor({}, "primary");
  const { keyboardHeight, isKeyboardVisible } = useKeyboardHeight();

  const inputStyles = useMemo(
    () => ({
      labelStyle: { fontSize: 14 },
      inputContainerStyle: {
        borderWidth: 2,
        borderRadius: BORDER_RADIUS,
      },
    }),
    []
  );
  return (
    <View style={[styles.container]}>
      {/* Header */}
      <View style={styles.header}>
        <Text variant="heading" style={{ fontSize: 24 }}>
          Ostram Field Agent
        </Text>
        <Text variant="subtitle" style={{ textAlign: "center", fontSize: 16 }}>
          Please sign in to continue
        </Text>
      </View>

      <View style={styles.signin}>
        <Input
          placeholder="Email"
          icon={Mail}
          value=""
          error=""
          keyboardType="email-address"
          {...inputStyles}
        />

        <Input
          placeholder="Password"
          icon={Lock}
          value=""
          error=""
          secureTextEntry
          {...inputStyles}
        />

        <View style={{ alignItems: "flex-end", marginVertical: 12 }}>
          <Text
            variant="link"
            style={{ textDecorationLine: "none", color: primary }}
          >
            Forgot Password?
          </Text>
        </View>

        <Button size="lg">Login</Button>
        <View style={{ display: isKeyboardVisible ? "none" : "flex" }}>
          <Separator label="OR" />
          <Button
            icon={Fingerprint}
            size="lg"
            variant="outline"
            disabled
            textStyle={{ fontWeight:"bold" }}
            style={{ backgroundColor: `${primary}20`, marginTop:10 }}
          >
            Use Biometrics
          </Button>
        </View>
      </View>

      <View style={styles.footer}>
        <Text
          variant="caption"
          style={{ fontSize: 14, display: isKeyboardVisible ? "none" : "flex" }}
        >
          Powered by SAMS
        </Text>
      </View>
    </View>
  );
};

export default login;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    paddingTop: 64,
    alignItems: "center",
    justifyContent: "center",
  },
  signin: {
    flex: 1,
    justifyContent: "center",
    gap: 15,
  },
  footer: {
    paddingBottom: 20,
    alignItems: "center",
    justifyContent: "center",
  },
});
