import { Redirect, Stack } from 'expo-router';


export default function AuthLayout() {
  const isAuthenticated = true;

  // Redirect to authenticated route if already authenticated
  if (isAuthenticated) {
    return <Redirect href="/" />; // Replace with the path to your authenticated route
  }

  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="login" />
    </Stack>
  );
}