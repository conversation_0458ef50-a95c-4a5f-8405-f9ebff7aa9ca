const lightColors = {
  // Custom primary color
  primary: '#1173d4',
  primaryForeground: '#FFFFFF',

  // Base colors - light theme
  background: '#f6f7f8',
  foreground: '#111418',

  // Surface colors
  card: '#ffffff',
  cardForeground: '#111418',
  surface: '#ffffff',

  // Popover colors
  popover: '#ffffff',
  popoverForeground: '#111418',

  // Secondary colors
  secondary: '#f6f7f8',
  secondaryForeground: '#111418',

  // Muted and subtle colors
  muted: '#617589',
  mutedForeground: '#617589',
  subtle: '#617589',

  // Accent colors
  accent: '#f6f7f8',
  accentForeground: '#111418',

  // Destructive colors
  destructive: '#ef4444',
  destructiveForeground: '#FFFFFF',

  // Border and input
  border: '#dbe0e6',
  input: '#dbe0e6',
  ring: '#1173d4',

  // Text colors
  text: '#111418',
  textMuted: '#617589',

  // Legacy support for existing components
  tint: '#1173d4',
  icon: '#617589',
  tabIconDefault: '#617589',
  tabIconSelected: '#1173d4',

  // Default buttons, links, Send button, selected tabs
  blue: '#1173d4',

  // Success states, FaceTime buttons, completed tasks
  green: '#34C759',

  // Delete buttons, error states, critical alertsgreen
  red: '#FF3B30',

  // VoiceOver highlights, warning states
  orange: '#FF9500',

  // Notes app accent, Reminders highlights
  yellow: '#FFCC00',

  // Pink accent color for various UI elements
  pink: '#FF2D92',

  // Purple accent for creative apps and features
  purple: '#AF52DE',

  // Teal accent for communication features
  teal: '#5AC8FA',

  // Indigo accent for system features
  indigo: '#5856D6',
};

const darkColors = {
  // Custom primary color (lighter in dark mode)
  primary: '#1173d4',
  primaryForeground: '#FFFFFF',

  // Base colors - dark theme
  background: '#101922',
  foreground: '#f6f7f8',

  // Surface colors
  card: '#1a2530',
  cardForeground: '#f6f7f8',
  surface: '#1a2530',

  // Popover colors
  popover: '#1a2530',
  popoverForeground: '#f6f7f8',

  // Secondary colors
  secondary: '#1a2530',
  secondaryForeground: '#f6f7f8',

  // Muted and subtle colors
  muted: '#a0b3c6',
  mutedForeground: '#a0b3c6',
  subtle: '#a0b3c6',

  // Accent colors
  accent: '#1a2530',
  accentForeground: '#f6f7f8',

  // Destructive colors
  destructive: '#dc2626',
  destructiveForeground: '#FFFFFF',

  // Border and input
  border: '#2c3a47',
  input: '#2c3a47',
  ring: '#1173d4',

  // Text colors
  text: '#f6f7f8',
  textMuted: '#a0b3c6',

  // Legacy support for existing components
  tint: '#1173d4',
  icon: '#a0b3c6',
  tabIconDefault: '#a0b3c6',
  tabIconSelected: '#1173d4',

  // Default buttons, links, Send button, selected tabs
  blue: '#1173d4',

  // Success states, FaceTime buttons, completed tasks
  green: '#30D158',

  // Delete buttons, error states, critical alerts
  red: '#FF453A',

  // VoiceOver highlights, warning states
  orange: '#FF9F0A',

  // Notes app accent, Reminders highlights
  yellow: '#FFD60A',

  // Pink accent color for various UI elements
  pink: '#FF375F',

  // Purple accent for creative apps and features
  purple: '#BF5AF2',

  // Teal accent for communication features
  teal: '#64D2FF',

  // Indigo accent for system features
  indigo: '#5E5CE6',
};

export const Colors = {
  light: lightColors,
  dark: darkColors,
};

// Export individual color schemes for easier access
export { darkColors, lightColors };

// Utility type for color keys
export type ColorKeys = keyof typeof lightColors;
