{"expo": {"name": "sacco-field-agent-app", "slug": "sacco-field-agent-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "sacco-field-agent-app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#0A0A0A"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#0A0A0A"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/icon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#0A0A0A"}], "expo-font"], "experiments": {"typedRoutes": true}}}