export type Member = {
  id: string;
  name: string;
  memberId: string;
  avatar: string;
  lastActive: string; // ISO date string
  status: 'overdue' | 'active' | 'new' | 'high_balance';
};

export async function fetchMembers(): Promise<Member[]> {
  // Use a static require so Metro can bundle the JSON for native and web
  const data = require('../data/members.json') as Member[];

  // Simulate async fetch latency
  return new Promise((resolve) => setTimeout(() => resolve(data), 150));
}

export function timeAgo(iso: string): string {
  const date = new Date(iso);
  const now = new Date();
  const diff = Math.max(0, now.getTime() - date.getTime());

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;

  if (diff >= week) return `${Math.floor(diff / week)}w ago`;
  if (diff >= day) return `${Math.floor(diff / day)}d ago`;
  if (diff >= hour) return `${Math.floor(diff / hour)}h ago`;
  return `${Math.max(1, Math.floor(diff / minute))}m ago`;
}